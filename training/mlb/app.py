from flask import Flask, render_template, request, jsonify
from flask_sqlalchemy import SQLAlchemy
from flask_migrate import Migrate
from apscheduler.schedulers.background import BackgroundScheduler
from datetime import datetime, timedelta
import atexit
import os

from config import config
from models.database import db

def create_app(config_name=None):
    """應用工廠函數"""
    app = Flask(__name__)
    
    # 選擇配置
    if config_name is None:
        config_name = os.environ.get('FLASK_ENV', 'development')
    
    app.config.from_object(config[config_name])
    
    # 確保data目錄存在
    data_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'data')
    os.makedirs(data_dir, exist_ok=True)
    
    # 初始化擴展
    db.init_app(app)
    migrate = Migrate(app, db)
    
    # 註冊藍圖（稍後實現）
    register_blueprints(app)
    
    # 創建數據庫表
    with app.app_context():
        db.create_all()
    
    # 設置定時任務（稍後實現）
    # setup_scheduler(app)
    
    return app

def register_blueprints(app):
    """註冊藍圖"""
    from views.dashboard import dashboard_bp
    from views.teams import teams_bp
    from views.games import games_bp

    app.register_blueprint(dashboard_bp, url_prefix='/dashboard')
    app.register_blueprint(teams_bp, url_prefix='/teams')
    app.register_blueprint(games_bp, url_prefix='/games')

def setup_scheduler(app):
    """設置背景定時任務"""
    scheduler = BackgroundScheduler()
    
    # 每日數據更新任務
    scheduler.add_job(
        func=update_daily_data,
        trigger="cron",
        hour=6,  # 每天早上6點執行
        minute=0,
        id='daily_data_update'
    )
    
    # 每小時預測更新任務
    scheduler.add_job(
        func=update_predictions,
        trigger="cron",
        minute=0,  # 每小時的0分執行
        id='hourly_prediction_update'
    )
    
    scheduler.start()
    atexit.register(lambda: scheduler.shutdown())

def update_daily_data():
    """每日數據更新任務"""
    # 稍後實現
    pass

def update_predictions():
    """更新比賽預測"""
    # 稍後實現
    pass

# 創建應用實例
app = create_app()

# 主頁路由
@app.route('/')
def index():
    """重定向到儀表板"""
    from flask import redirect, url_for
    return redirect(url_for('dashboard.dashboard'))

@app.route('/api')
def api_index():
    return jsonify({
        'message': 'MLB 預測系統 API',
        'status': 'running',
        'timestamp': datetime.now().isoformat()
    })

@app.route('/health')
def health_check():
    """健康檢查端點"""
    return jsonify({
        'status': 'healthy',
        'database': 'connected',
        'timestamp': datetime.now().isoformat()
    })

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5555)
