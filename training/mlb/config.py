import os
from datetime import timedelta

class Config:
    # Flask基本配置
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'your-secret-key-here'
    
    # 數據庫配置
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL') or 'sqlite:///mlb_data.db'
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    
    # 數據更新配置
    DATA_UPDATE_INTERVAL = timedelta(hours=6)  # 每6小時更新一次
    PREDICTION_UPDATE_INTERVAL = timedelta(hours=1)  # 每小時更新預測
    
    # API配置
    MLB_API_BASE_URL = 'https://statsapi.mlb.com/api/v1'
    CACHE_TIMEOUT = 300  # 5分鐘快取
    
    # 分頁配置
    GAMES_PER_PAGE = 20
    PREDICTIONS_PER_PAGE = 10

class DevelopmentConfig(Config):
    DEBUG = True
    
class ProductionConfig(Config):
    DEBUG = False

# 根據環境變數選擇配置
config = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'default': DevelopmentConfig
}
