#!/usr/bin/env python3
"""
顯示數據庫中的詳細信息
"""

import sys
import os
from datetime import date, datetime

# 添加當前目錄到Python路徑
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from models.database import db, Team, Game, TeamStats, Prediction

def show_database_info():
    """顯示數據庫中的詳細信息"""
    print("=== MLB 數據庫信息詳覽 ===")
    
    # 創建Flask應用上下文
    app = create_app()
    
    with app.app_context():
        print("\n📊 數據庫統計:")
        total_teams = Team.query.count()
        total_games = Game.query.count()
        total_stats = TeamStats.query.count()
        total_predictions = Prediction.query.count()
        
        print(f"  - 球隊數量: {total_teams}")
        print(f"  - 比賽數量: {total_games}")
        print(f"  - 球隊統計: {total_stats}")
        print(f"  - 預測記錄: {total_predictions}")
        
        print("\n🏟️ 所有球隊信息:")
        teams = Team.query.order_by(Team.league, Team.division, Team.team_name).all()
        
        current_league = None
        current_division = None
        
        for team in teams:
            # 顯示聯盟標題
            if team.league != current_league:
                current_league = team.league
                print(f"\n  📍 {team.league}:")
                current_division = None
            
            # 顯示分區標題
            if team.division != current_division:
                current_division = team.division
                print(f"    🏆 {team.division}:")
            
            # 顯示球隊信息
            print(f"      - {team.team_code:3} | {team.team_name:25} | {team.venue_name}")
        
        print("\n⚾ 比賽信息:")
        if total_games > 0:
            games = Game.query.order_by(Game.date.desc()).limit(10).all()
            print("  最近的比賽記錄:")
            for game in games:
                status_emoji = {
                    'completed': '✅',
                    'scheduled': '📅',
                    'in_progress': '⏳',
                    'postponed': '⏸️',
                    'cancelled': '❌'
                }.get(game.game_status, '❓')
                
                score_info = ""
                if game.home_score is not None and game.away_score is not None:
                    score_info = f" ({game.away_score}-{game.home_score})"
                
                print(f"    {status_emoji} {game.date} | {game.away_team} @ {game.home_team}{score_info}")
        else:
            print("  目前沒有比賽記錄")
        
        print("\n📈 球隊統計信息:")
        if total_stats > 0:
            stats = TeamStats.query.order_by(TeamStats.win_percentage.desc()).limit(5).all()
            print("  勝率最高的5支球隊:")
            for i, stat in enumerate(stats, 1):
                team = Team.query.filter_by(team_id=stat.team_id).first()
                team_name = team.team_code if team else f"Team-{stat.team_id}"
                print(f"    {i}. {team_name:3} | {stat.season} | {stat.wins:2}勝{stat.losses:2}敗 | 勝率:{stat.win_percentage:.3f}")
        else:
            print("  目前沒有球隊統計記錄")
        
        print("\n🔮 預測信息:")
        if total_predictions > 0:
            predictions = Prediction.query.order_by(Prediction.prediction_date.desc()).limit(5).all()
            print("  最近的預測記錄:")
            for pred in predictions:
                print(f"    📊 {pred.game_id} | 預測: {pred.predicted_away_score:.1f}-{pred.predicted_home_score:.1f} | 信心度:{pred.confidence:.1%}")
        else:
            print("  目前沒有預測記錄")
        
        print("\n🗄️ 數據庫表結構:")
        print("  已創建的表:")
        
        # 檢查表是否存在
        inspector = db.inspect(db.engine)
        tables = inspector.get_table_names()
        
        table_descriptions = {
            'teams': '球隊基本信息',
            'games': '比賽記錄',
            'team_stats': '球隊統計數據',
            'predictions': '比賽預測',
            'player_stats': '球員統計數據'
        }
        
        for table in tables:
            desc = table_descriptions.get(table, '未知表')
            columns = inspector.get_columns(table)
            print(f"    📋 {table} ({desc}) - {len(columns)} 個欄位")
        
        print(f"\n✅ 數據庫檔案位置: {app.config['SQLALCHEMY_DATABASE_URI']}")

if __name__ == '__main__':
    show_database_info()
