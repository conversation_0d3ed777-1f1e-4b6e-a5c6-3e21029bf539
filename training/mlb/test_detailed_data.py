#!/usr/bin/env python3
"""
測試詳細的比賽內容和球員資料獲取
"""

import sys
import os
from datetime import date, datetime

# 添加當前目錄到Python路徑
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from models.data_fetcher import MLBDataFetcher
from models.database import db, Game, GameDetail, Player, PlayerStats

def test_detailed_data():
    """測試詳細數據獲取功能"""
    print("=== MLB 詳細數據獲取測試 ===")
    
    # 創建Flask應用上下文
    app = create_app()
    
    with app.app_context():
        # 創建數據獲取器
        fetcher = MLBDataFetcher()
        
        print("\n1. 測試獲取比賽詳細內容...")
        try:
            # 選擇一場已完成的比賽
            completed_game = Game.query.filter_by(game_status='completed').first()
            
            if completed_game:
                print(f"測試比賽: {completed_game.away_team} @ {completed_game.home_team} ({completed_game.date})")
                print(f"比賽ID: {completed_game.game_id}")
                
                # 獲取比賽詳細內容
                game_detail = fetcher.get_game_detail(completed_game.game_id)
                
                if game_detail:
                    print("比賽詳細內容:")
                    print(f"  - 觀眾人數: {game_detail.get('attendance', 'N/A')}")
                    print(f"  - 比賽時長: {game_detail.get('game_duration', 'N/A')} 分鐘")
                    print(f"  - 天氣: {game_detail.get('weather_condition', 'N/A')}")
                    print(f"  - 風速: {game_detail.get('wind_speed', 'N/A')}")
                    print(f"  - 主隊先發: {game_detail.get('home_starting_pitcher', 'N/A')}")
                    print(f"  - 客隊先發: {game_detail.get('away_starting_pitcher', 'N/A')}")
                    print(f"  - 勝投: {game_detail.get('winning_pitcher', 'N/A')}")
                    print(f"  - 敗投: {game_detail.get('losing_pitcher', 'N/A')}")
                    print(f"  - 救援: {game_detail.get('save_pitcher', 'N/A')}")
                    print(f"  - 主隊安打: {game_detail.get('home_hits', 'N/A')}")
                    print(f"  - 客隊安打: {game_detail.get('away_hits', 'N/A')}")
                    print(f"  - 主隊失誤: {game_detail.get('home_errors', 'N/A')}")
                    print(f"  - 客隊失誤: {game_detail.get('away_errors', 'N/A')}")
                    
                    # 更新到數據庫
                    success = fetcher.update_game_details(completed_game.game_id)
                    if success:
                        print("  ✅ 比賽詳細內容已更新到數據庫")
                    else:
                        print("  ❌ 比賽詳細內容更新失敗")
                else:
                    print("  無法獲取比賽詳細內容")
            else:
                print("  數據庫中沒有已完成的比賽")
        except Exception as e:
            print(f"獲取比賽詳細內容失敗: {e}")
        
        print("\n2. 測試獲取球員基本信息...")
        try:
            # 測試一些知名球員ID
            test_player_ids = [660271, 545361, 592450]  # 一些MLB球員ID
            
            for player_id in test_player_ids:
                print(f"\n測試球員ID: {player_id}")
                
                player_info = fetcher.get_player_info(player_id)
                
                if player_info:
                    print(f"球員信息:")
                    print(f"  - 姓名: {player_info.get('full_name', 'N/A')}")
                    print(f"  - 生日: {player_info.get('birth_date', 'N/A')}")
                    print(f"  - 出生地: {player_info.get('birth_city', 'N/A')}, {player_info.get('birth_country', 'N/A')}")
                    print(f"  - 身高: {player_info.get('height', 'N/A')}")
                    print(f"  - 體重: {player_info.get('weight', 'N/A')} 磅")
                    print(f"  - 打擊: {player_info.get('bats', 'N/A')}")
                    print(f"  - 投球: {player_info.get('throws', 'N/A')}")
                    print(f"  - 位置: {player_info.get('primary_position', 'N/A')}")
                    print(f"  - MLB首秀: {player_info.get('mlb_debut', 'N/A')}")
                    print(f"  - 球衣號碼: {player_info.get('jersey_number', 'N/A')}")
                    
                    # 更新到數據庫
                    success = fetcher.update_player_info(player_id)
                    if success:
                        print("  ✅ 球員信息已更新到數據庫")
                    else:
                        print("  ❌ 球員信息更新失敗")
                else:
                    print(f"  無法獲取球員 {player_id} 的信息")
                
                # 避免API限制
                import time
                time.sleep(1)
                
        except Exception as e:
            print(f"獲取球員信息失敗: {e}")
        
        print("\n3. 測試獲取詳細球員統計...")
        try:
            # 選擇一支球隊獲取球員統計
            test_team = Game.query.first()
            if test_team:
                # 獲取主隊信息
                from models.database import Team
                team = Team.query.filter_by(team_code=test_team.home_team).first()
                
                if team:
                    print(f"獲取球隊 {team.team_code} 的詳細球員統計...")
                    
                    players_data = fetcher.get_player_stats(team.team_id, 2024)
                    
                    if players_data:
                        print(f"成功獲取 {len(players_data)} 名球員的詳細統計")
                        
                        # 顯示前3名球員的詳細統計
                        print("\n球員詳細統計樣本:")
                        for i, player in enumerate(players_data[:3]):
                            print(f"\n  球員 {i+1}: {player.get('player_name', 'Unknown')}")
                            print(f"    位置: {player.get('position', 'N/A')}")
                            print(f"    出賽: {player.get('games_played', 0)} 場")
                            
                            # 打擊統計
                            if player.get('at_bats', 0) > 0:
                                print(f"    打擊統計:")
                                print(f"      - 打數: {player.get('at_bats', 0)}")
                                print(f"      - 安打: {player.get('hits', 0)}")
                                print(f"      - 全壘打: {player.get('home_runs', 0)}")
                                print(f"      - 打點: {player.get('rbi', 0)}")
                                print(f"      - 打擊率: {player.get('batting_avg', 0):.3f}")
                                print(f"      - OPS: {player.get('ops', 0):.3f}")
                            
                            # 投手統計
                            if player.get('innings_pitched', 0) > 0:
                                print(f"    投手統計:")
                                print(f"      - 投球局數: {player.get('innings_pitched', 0):.1f}")
                                print(f"      - 勝場: {player.get('wins', 0)}")
                                print(f"      - 敗場: {player.get('losses', 0)}")
                                print(f"      - 防禦率: {player.get('era', 0):.2f}")
                                print(f"      - WHIP: {player.get('whip', 0):.3f}")
                                print(f"      - 三振: {player.get('strikeouts_pitching', 0)}")
                    else:
                        print("  無法獲取球員統計數據")
        except Exception as e:
            print(f"獲取詳細球員統計失敗: {e}")
        
        print("\n4. 數據庫統計摘要...")
        try:
            total_games = Game.query.count()
            total_game_details = GameDetail.query.count()
            total_players = Player.query.count()
            total_player_stats = PlayerStats.query.count()
            
            print(f"  - 比賽記錄: {total_games}")
            print(f"  - 比賽詳細內容: {total_game_details}")
            print(f"  - 球員基本信息: {total_players}")
            print(f"  - 球員統計記錄: {total_player_stats}")
            
            # 顯示有詳細內容的比賽
            if total_game_details > 0:
                detailed_games = db.session.query(Game, GameDetail).join(
                    GameDetail, Game.game_id == GameDetail.game_id
                ).limit(5).all()
                
                print(f"\n  有詳細內容的比賽樣本:")
                for game, detail in detailed_games:
                    print(f"    - {game.away_team} @ {game.home_team} ({game.date})")
                    print(f"      觀眾: {detail.attendance or 'N/A'}, 安打: {detail.away_hits or 0}-{detail.home_hits or 0}")
            
            # 顯示球員信息
            if total_players > 0:
                players = Player.query.limit(5).all()
                print(f"\n  球員信息樣本:")
                for player in players:
                    print(f"    - {player.full_name} ({player.primary_position or 'N/A'})")
                    print(f"      球隊ID: {player.current_team_id or 'N/A'}, 號碼: {player.jersey_number or 'N/A'}")
            
        except Exception as e:
            print(f"獲取統計摘要失敗: {e}")

if __name__ == '__main__':
    test_detailed_data()
