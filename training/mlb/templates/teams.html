{% extends "base.html" %}

{% block title %}球隊 - MLB 預測系統{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h1 class="mb-4">
            <i class="fas fa-users"></i> MLB 球隊
        </h1>
    </div>
</div>

<!-- 聯盟標籤 -->
<ul class="nav nav-tabs mb-4" id="leagueTabs" role="tablist">
    <li class="nav-item" role="presentation">
        <button class="nav-link active" id="all-tab" data-bs-toggle="tab" data-bs-target="#all" type="button" role="tab">
            <i class="fas fa-globe"></i> 所有球隊
        </button>
    </li>
    <li class="nav-item" role="presentation">
        <button class="nav-link" id="al-tab" data-bs-toggle="tab" data-bs-target="#al" type="button" role="tab">
            <i class="fas fa-flag"></i> 美國聯盟
        </button>
    </li>
    <li class="nav-item" role="presentation">
        <button class="nav-link" id="nl-tab" data-bs-toggle="tab" data-bs-target="#nl" type="button" role="tab">
            <i class="fas fa-flag"></i> 國家聯盟
        </button>
    </li>
</ul>

<div class="tab-content" id="leagueTabContent">
    <!-- 所有球隊 -->
    <div class="tab-pane fade show active" id="all" role="tabpanel">
        <div class="row">
            {% for team in teams %}
            <div class="col-md-6 col-lg-4 mb-3">
                <div class="card h-100">
                    <div class="card-header bg-primary text-white">
                        <h6 class="mb-0">
                            <strong>{{ team.team_code }}</strong> - {{ team.team_name }}
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-12">
                                <small class="text-muted">聯盟:</small> {{ team.league }}<br>
                                <small class="text-muted">分區:</small> {{ team.division }}<br>
                                <small class="text-muted">主場:</small> {{ team.venue_name }}
                            </div>
                        </div>
                    </div>
                    <div class="card-footer">
                        <div class="btn-group w-100" role="group">
                            <a href="/teams/{{ team.team_id }}" class="btn btn-sm btn-outline-primary">
                                <i class="fas fa-info-circle"></i> 詳情
                            </a>
                            <a href="/teams/{{ team.team_id }}/stats" class="btn btn-sm btn-outline-success">
                                <i class="fas fa-chart-bar"></i> 統計
                            </a>
                            <a href="/teams/{{ team.team_id }}/players" class="btn btn-sm btn-outline-info">
                                <i class="fas fa-users"></i> 球員
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
    
    <!-- 美國聯盟 -->
    <div class="tab-pane fade" id="al" role="tabpanel">
        {% set al_teams = teams | selectattr("league", "equalto", "American League") | list %}
        {% if al_teams %}
            <!-- 按分區分組 -->
            {% for division in ["American League East", "American League Central", "American League West"] %}
                {% set division_teams = al_teams | selectattr("division", "equalto", division) | list %}
                {% if division_teams %}
                <h4 class="mt-4 mb-3">{{ division }}</h4>
                <div class="row">
                    {% for team in division_teams %}
                    <div class="col-md-6 col-lg-4 mb-3">
                        <div class="card">
                            <div class="card-body">
                                <h6 class="card-title">{{ team.team_code }} - {{ team.team_name }}</h6>
                                <p class="card-text">
                                    <small class="text-muted">主場: {{ team.venue_name }}</small>
                                </p>
                                <a href="/teams/{{ team.team_id }}" class="btn btn-sm btn-primary">查看詳情</a>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                {% endif %}
            {% endfor %}
        {% endif %}
    </div>
    
    <!-- 國家聯盟 -->
    <div class="tab-pane fade" id="nl" role="tabpanel">
        {% set nl_teams = teams | selectattr("league", "equalto", "National League") | list %}
        {% if nl_teams %}
            <!-- 按分區分組 -->
            {% for division in ["National League East", "National League Central", "National League West"] %}
                {% set division_teams = nl_teams | selectattr("division", "equalto", division) | list %}
                {% if division_teams %}
                <h4 class="mt-4 mb-3">{{ division }}</h4>
                <div class="row">
                    {% for team in division_teams %}
                    <div class="col-md-6 col-lg-4 mb-3">
                        <div class="card">
                            <div class="card-body">
                                <h6 class="card-title">{{ team.team_code }} - {{ team.team_name }}</h6>
                                <p class="card-text">
                                    <small class="text-muted">主場: {{ team.venue_name }}</small>
                                </p>
                                <a href="/teams/{{ team.team_id }}" class="btn btn-sm btn-success">查看詳情</a>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                {% endif %}
            {% endfor %}
        {% endif %}
    </div>
</div>
{% endblock %}
