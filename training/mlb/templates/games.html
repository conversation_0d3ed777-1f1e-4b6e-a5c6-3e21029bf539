{% extends "base.html" %}

{% block title %}比賽 - MLB 預測系統{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h1 class="mb-4">
            <i class="fas fa-calendar-alt"></i> MLB 比賽
        </h1>
    </div>
</div>

<!-- 篩選器 -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-3">
                <label for="date" class="form-label">日期</label>
                <input type="date" class="form-control" id="date" name="date" value="{{ selected_date }}">
            </div>
            <div class="col-md-3">
                <label for="status" class="form-label">狀態</label>
                <select class="form-select" id="status" name="status">
                    <option value="">所有狀態</option>
                    <option value="scheduled" {% if selected_status == 'scheduled' %}selected{% endif %}>預定</option>
                    <option value="in_progress" {% if selected_status == 'in_progress' %}selected{% endif %}>進行中</option>
                    <option value="completed" {% if selected_status == 'completed' %}selected{% endif %}>已結束</option>
                    <option value="postponed" {% if selected_status == 'postponed' %}selected{% endif %}>延期</option>
                </select>
            </div>
            <div class="col-md-3">
                <label for="team" class="form-label">球隊</label>
                <select class="form-select" id="team" name="team">
                    <option value="">所有球隊</option>
                    {% for team in teams %}
                    <option value="{{ team.team_code }}" {% if selected_team == team.team_code %}selected{% endif %}>
                        {{ team.team_code }} - {{ team.team_name }}
                    </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-3">
                <label class="form-label">&nbsp;</label>
                <div class="d-grid">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search"></i> 搜尋
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- 比賽列表 -->
{% if games %}
    <div class="row">
        {% for game in games %}
        <div class="col-md-6 col-lg-4 mb-3">
            <div class="card game-card h-100">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <small class="text-muted">{{ game.date.strftime('%Y-%m-%d') }}</small>
                    <span class="badge bg-{% if game.game_status == 'completed' %}success{% elif game.game_status == 'in_progress' %}warning{% elif game.game_status == 'scheduled' %}secondary{% else %}danger{% endif %}">
                        {% if game.game_status == 'completed' %}
                            <i class="fas fa-check-circle"></i> 已結束
                        {% elif game.game_status == 'in_progress' %}
                            <i class="fas fa-play-circle"></i> 進行中
                        {% elif game.game_status == 'scheduled' %}
                            <i class="fas fa-clock"></i> 預定
                        {% else %}
                            {{ game.game_status }}
                        {% endif %}
                    </span>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-5">
                            <h5 class="mb-1">{{ game.away_team }}</h5>
                            <small class="text-muted">客隊</small>
                            {% if game.away_score is not none %}
                                <div class="h3 mt-2 text-primary">{{ game.away_score }}</div>
                            {% endif %}
                        </div>
                        <div class="col-2 d-flex align-items-center justify-content-center">
                            <span class="h4 text-muted">@</span>
                        </div>
                        <div class="col-5">
                            <h5 class="mb-1">{{ game.home_team }}</h5>
                            <small class="text-muted">主隊</small>
                            {% if game.home_score is not none %}
                                <div class="h3 mt-2 text-success">{{ game.home_score }}</div>
                            {% endif %}
                        </div>
                    </div>
                    
                    {% if game.venue %}
                    <div class="text-center mt-3">
                        <small class="text-muted">
                            <i class="fas fa-map-marker-alt"></i> {{ game.venue }}
                        </small>
                    </div>
                    {% endif %}
                    
                    {% if game.inning and game.inning_state %}
                    <div class="text-center mt-2">
                        <small class="text-info">
                            <i class="fas fa-baseball-ball"></i> 
                            {{ game.inning }}局 {{ game.inning_state }}
                        </small>
                    </div>
                    {% endif %}
                </div>
                <div class="card-footer">
                    <div class="btn-group w-100" role="group">
                        <a href="/games/{{ game.game_id }}" class="btn btn-sm btn-outline-primary">
                            <i class="fas fa-info-circle"></i> 詳情
                        </a>
                        {% if game.game_status == 'scheduled' %}
                        <a href="/predictions/{{ game.game_id }}" class="btn btn-sm btn-outline-success">
                            <i class="fas fa-crystal-ball"></i> 預測
                        </a>
                        {% endif %}
                        <a href="/games/{{ game.game_id }}/analysis" class="btn btn-sm btn-outline-info">
                            <i class="fas fa-chart-line"></i> 分析
                        </a>
                    </div>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>
    
    <!-- 分頁 -->
    {% if pagination %}
    <nav aria-label="比賽分頁">
        <ul class="pagination justify-content-center">
            {% if pagination.has_prev %}
            <li class="page-item">
                <a class="page-link" href="{{ url_for('games.games_list', page=pagination.prev_num, date=selected_date, status=selected_status, team=selected_team) }}">
                    <i class="fas fa-chevron-left"></i> 上一頁
                </a>
            </li>
            {% endif %}
            
            {% for page_num in pagination.iter_pages() %}
                {% if page_num %}
                    {% if page_num != pagination.page %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('games.games_list', page=page_num, date=selected_date, status=selected_status, team=selected_team) }}">
                            {{ page_num }}
                        </a>
                    </li>
                    {% else %}
                    <li class="page-item active">
                        <span class="page-link">{{ page_num }}</span>
                    </li>
                    {% endif %}
                {% else %}
                <li class="page-item disabled">
                    <span class="page-link">…</span>
                </li>
                {% endif %}
            {% endfor %}
            
            {% if pagination.has_next %}
            <li class="page-item">
                <a class="page-link" href="{{ url_for('games.games_list', page=pagination.next_num, date=selected_date, status=selected_status, team=selected_team) }}">
                    下一頁 <i class="fas fa-chevron-right"></i>
                </a>
            </li>
            {% endif %}
        </ul>
    </nav>
    {% endif %}
{% else %}
    <div class="text-center py-5">
        <i class="fas fa-calendar-times fa-3x text-muted mb-3"></i>
        <h4 class="text-muted">沒有找到比賽記錄</h4>
        <p class="text-muted">請嘗試調整篩選條件或選擇其他日期</p>
    </div>
{% endif %}
{% endblock %}
