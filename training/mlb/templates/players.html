{% extends "base.html" %}

{% block title %}球員資料{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-users"></i> 球員資料</h2>
                <div class="btn-group" role="group">
                    <a href="{{ url_for('players.players_list') }}" class="btn btn-outline-primary">所有球員</a>
                    <a href="{{ url_for('players.players_stats') }}" class="btn btn-outline-info">統計排行</a>
                </div>
            </div>

            <!-- 篩選器 -->
            <div class="card mb-4">
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-3">
                            <label for="search" class="form-label">搜索球員</label>
                            <input type="text" class="form-control" id="search" name="search" 
                                   value="{{ search_query }}" placeholder="輸入球員姓名">
                        </div>
                        <div class="col-md-3">
                            <label for="team" class="form-label">球隊</label>
                            <select class="form-select" id="team" name="team">
                                <option value="">所有球隊</option>
                                {% for team in teams %}
                                <option value="{{ team.team_id }}" {% if team_filter == team.team_id|string %}selected{% endif %}>
                                    {{ team.team_code }} - {{ team.team_name }}
                                </option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="position" class="form-label">位置</label>
                            <select class="form-select" id="position" name="position">
                                <option value="">所有位置</option>
                                {% for pos in positions %}
                                <option value="{{ pos }}" {% if position_filter == pos %}selected{% endif %}>{{ pos }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-3 d-flex align-items-end">
                            <button type="submit" class="btn btn-primary me-2">搜索</button>
                            <a href="{{ url_for('players.players_list') }}" class="btn btn-outline-secondary">清除</a>
                        </div>
                    </form>
                </div>
            </div>

            <!-- 球員列表 -->
            {% if players %}
                <div class="row">
                    {% for player in players %}
                    <div class="col-lg-6 col-xl-4 mb-4">
                        <div class="card h-100">
                            <div class="card-body">
                                <div class="d-flex align-items-start mb-3">
                                    <div class="flex-grow-1">
                                        <h5 class="card-title mb-1">
                                            <a href="{{ url_for('players.player_detail', player_id=player.player_id) }}" 
                                               class="text-decoration-none">{{ player.full_name }}</a>
                                        </h5>
                                        <div class="text-muted small">
                                            {% if player.jersey_number %}#{{ player.jersey_number }}{% endif %}
                                            {% if player.primary_position %}{{ player.primary_position }}{% endif %}
                                        </div>
                                    </div>
                                    {% if player.active %}
                                        <span class="badge bg-success">現役</span>
                                    {% else %}
                                        <span class="badge bg-secondary">退役</span>
                                    {% endif %}
                                </div>

                                <div class="row text-center mb-3">
                                    <div class="col-4">
                                        <div class="small text-muted">身高</div>
                                        <div>{{ player.height or 'N/A' }}</div>
                                    </div>
                                    <div class="col-4">
                                        <div class="small text-muted">體重</div>
                                        <div>{{ player.weight or 'N/A' }}</div>
                                    </div>
                                    <div class="col-4">
                                        <div class="small text-muted">年齡</div>
                                        <div>
                                            {% if player.birth_date %}
                                                {{ ((datetime.now().date() - player.birth_date).days / 365.25)|int }}
                                            {% else %}
                                                N/A
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>

                                <div class="row text-center mb-3">
                                    <div class="col-6">
                                        <div class="small text-muted">打擊</div>
                                        <div>{{ player.bats or 'N/A' }}</div>
                                    </div>
                                    <div class="col-6">
                                        <div class="small text-muted">投球</div>
                                        <div>{{ player.throws or 'N/A' }}</div>
                                    </div>
                                </div>

                                {% if player.birth_city or player.birth_country %}
                                <div class="text-center mb-3">
                                    <small class="text-muted">
                                        <i class="fas fa-map-marker-alt"></i>
                                        {{ player.birth_city }}{% if player.birth_city and player.birth_country %}, {% endif %}{{ player.birth_country }}
                                    </small>
                                </div>
                                {% endif %}

                                {% if player.mlb_debut %}
                                <div class="text-center mb-3">
                                    <small class="text-muted">
                                        <i class="fas fa-calendar-alt"></i>
                                        MLB首秀: {{ player.mlb_debut.strftime('%Y-%m-%d') }}
                                    </small>
                                </div>
                                {% endif %}
                            </div>
                            <div class="card-footer">
                                <div class="d-flex justify-content-between align-items-center">
                                    <small class="text-muted">
                                        {% if player.current_team_id %}
                                            {% for team in teams %}
                                                {% if team.team_id == player.current_team_id %}
                                                    {{ team.team_code }}
                                                {% endif %}
                                            {% endfor %}
                                        {% else %}
                                            自由球員
                                        {% endif %}
                                    </small>
                                    <a href="{{ url_for('players.player_detail', player_id=player.player_id) }}" 
                                       class="btn btn-sm btn-outline-primary">查看詳情</a>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>

                <!-- 分頁 -->
                {% if pagination.pages > 1 %}
                <nav aria-label="球員分頁">
                    <ul class="pagination justify-content-center">
                        {% if pagination.has_prev %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('players.players_list', page=pagination.prev_num, search=search_query, team=team_filter, position=position_filter) }}">上一頁</a>
                            </li>
                        {% endif %}
                        
                        {% for page_num in pagination.iter_pages() %}
                            {% if page_num %}
                                {% if page_num != pagination.page %}
                                    <li class="page-item">
                                        <a class="page-link" href="{{ url_for('players.players_list', page=page_num, search=search_query, team=team_filter, position=position_filter) }}">{{ page_num }}</a>
                                    </li>
                                {% else %}
                                    <li class="page-item active">
                                        <span class="page-link">{{ page_num }}</span>
                                    </li>
                                {% endif %}
                            {% else %}
                                <li class="page-item disabled">
                                    <span class="page-link">…</span>
                                </li>
                            {% endif %}
                        {% endfor %}
                        
                        {% if pagination.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('players.players_list', page=pagination.next_num, search=search_query, team=team_filter, position=position_filter) }}">下一頁</a>
                            </li>
                        {% endif %}
                    </ul>
                </nav>
                {% endif %}
            {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-user-slash fa-3x text-muted mb-3"></i>
                    <h4 class="text-muted">沒有找到球員</h4>
                    <p class="text-muted">請嘗試調整搜索條件</p>
                </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// 搜索功能增強
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.getElementById('search');
    let searchTimeout;
    
    searchInput.addEventListener('input', function() {
        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(function() {
            // 可以在這裡添加即時搜索功能
        }, 500);
    });
});
</script>
{% endblock %}
