from flask import Blueprint, render_template, request, jsonify
from models.database import Player, PlayerStats, Team, db
from datetime import datetime, date
from sqlalchemy import or_, and_, func, desc

players_bp = Blueprint('players', __name__)

@players_bp.route('/')
def players_list():
    """球員列表頁面"""
    page = request.args.get('page', 1, type=int)
    team_filter = request.args.get('team', '')
    position_filter = request.args.get('position', '')
    search_query = request.args.get('search', '')
    
    # 建立查詢
    query = Player.query.filter(Player.active == True)
    
    # 球隊篩選
    if team_filter:
        query = query.filter(Player.current_team_id == team_filter)
    
    # 位置篩選
    if position_filter:
        query = query.filter(Player.primary_position == position_filter)
    
    # 搜索篩選
    if search_query:
        query = query.filter(
            or_(
                Player.full_name.contains(search_query),
                Player.first_name.contains(search_query),
                Player.last_name.contains(search_query)
            )
        )
    
    # 分頁
    players = query.order_by(Player.full_name).paginate(
        page=page, per_page=20, error_out=False
    )
    
    # 獲取所有球隊和位置用於篩選
    teams = Team.query.order_by(Team.team_code).all()
    positions = db.session.query(Player.primary_position).distinct().filter(
        Player.primary_position.isnot(None)
    ).all()
    positions = [pos[0] for pos in positions if pos[0]]
    
    return render_template('players.html',
                         players=players.items,
                         pagination=players,
                         teams=teams,
                         positions=sorted(positions),
                         team_filter=team_filter,
                         position_filter=position_filter,
                         search_query=search_query)

@players_bp.route('/<int:player_id>')
def player_detail(player_id):
    """球員詳情頁面"""
    player = Player.query.filter_by(player_id=player_id).first_or_404()
    
    # 獲取球員統計
    current_year = datetime.now().year
    current_stats = PlayerStats.query.filter_by(
        player_id=player_id,
        season=current_year
    ).first()
    
    # 獲取歷史統計
    historical_stats = PlayerStats.query.filter_by(
        player_id=player_id
    ).order_by(desc(PlayerStats.season)).all()
    
    # 獲取球隊信息
    team = None
    if player.current_team_id:
        team = Team.query.filter_by(team_id=player.current_team_id).first()
    
    # 計算生涯統計
    career_stats = calculate_career_stats(historical_stats)
    
    return render_template('player_detail.html',
                         player=player,
                         team=team,
                         current_stats=current_stats,
                         historical_stats=historical_stats,
                         career_stats=career_stats)

@players_bp.route('/stats')
def players_stats():
    """球員統計排行榜"""
    stat_type = request.args.get('stat', 'batting_avg')
    season = request.args.get('season', datetime.now().year, type=int)
    position = request.args.get('position', '')
    
    # 建立查詢
    query = db.session.query(PlayerStats, Player).join(
        Player, PlayerStats.player_id == Player.player_id
    ).filter(PlayerStats.season == season)
    
    # 位置篩選
    if position:
        query = query.filter(Player.primary_position == position)
    
    # 根據統計類型排序
    if stat_type == 'batting_avg':
        query = query.filter(PlayerStats.at_bats >= 100).order_by(desc(PlayerStats.batting_avg))
    elif stat_type == 'home_runs':
        query = query.order_by(desc(PlayerStats.home_runs))
    elif stat_type == 'rbi':
        query = query.order_by(desc(PlayerStats.rbi))
    elif stat_type == 'era':
        query = query.filter(PlayerStats.innings_pitched >= 50).order_by(PlayerStats.era)
    elif stat_type == 'strikeouts':
        query = query.order_by(desc(PlayerStats.strikeouts))
    else:
        query = query.order_by(desc(PlayerStats.batting_avg))
    
    # 限制結果數量
    stats = query.limit(50).all()
    
    # 獲取可用的位置
    positions = db.session.query(Player.primary_position).distinct().filter(
        Player.primary_position.isnot(None)
    ).all()
    positions = [pos[0] for pos in positions if pos[0]]
    
    return render_template('players_stats.html',
                         stats=stats,
                         stat_type=stat_type,
                         season=season,
                         position=position,
                         positions=sorted(positions))

@players_bp.route('/api/<int:player_id>')
def api_player_detail(player_id):
    """API端點：球員詳情"""
    player = Player.query.filter_by(player_id=player_id).first_or_404()
    
    # 獲取當前賽季統計
    current_year = datetime.now().year
    current_stats = PlayerStats.query.filter_by(
        player_id=player_id,
        season=current_year
    ).first()
    
    player_data = player.to_dict()
    if current_stats:
        player_data['current_stats'] = current_stats.to_dict()
    
    return jsonify(player_data)

@players_bp.route('/api/search')
def api_player_search():
    """API端點：球員搜索"""
    query = request.args.get('q', '')
    limit = request.args.get('limit', 10, type=int)
    
    if not query:
        return jsonify([])
    
    players = Player.query.filter(
        or_(
            Player.full_name.contains(query),
            Player.first_name.contains(query),
            Player.last_name.contains(query)
        ),
        Player.active == True
    ).limit(limit).all()
    
    result = []
    for player in players:
        result.append({
            'player_id': player.player_id,
            'full_name': player.full_name,
            'position': player.primary_position,
            'team_id': player.current_team_id
        })
    
    return jsonify(result)

@players_bp.route('/api/stats/leaders')
def api_stats_leaders():
    """API端點：統計領先者"""
    stat_type = request.args.get('stat', 'batting_avg')
    season = request.args.get('season', datetime.now().year, type=int)
    limit = request.args.get('limit', 10, type=int)
    
    query = db.session.query(PlayerStats, Player).join(
        Player, PlayerStats.player_id == Player.player_id
    ).filter(PlayerStats.season == season)
    
    # 根據統計類型排序
    if stat_type == 'batting_avg':
        query = query.filter(PlayerStats.at_bats >= 100).order_by(desc(PlayerStats.batting_avg))
    elif stat_type == 'home_runs':
        query = query.order_by(desc(PlayerStats.home_runs))
    elif stat_type == 'rbi':
        query = query.order_by(desc(PlayerStats.rbi))
    elif stat_type == 'era':
        query = query.filter(PlayerStats.innings_pitched >= 50).order_by(PlayerStats.era)
    elif stat_type == 'strikeouts':
        query = query.order_by(desc(PlayerStats.strikeouts))
    
    stats = query.limit(limit).all()
    
    result = []
    for player_stats, player in stats:
        result.append({
            'player': player.to_dict(),
            'stats': player_stats.to_dict()
        })
    
    return jsonify(result)

def calculate_career_stats(historical_stats):
    """計算生涯統計"""
    if not historical_stats:
        return None
    
    career = {
        'seasons': len(historical_stats),
        'total_games': sum(stats.games_played for stats in historical_stats),
        'total_at_bats': sum(stats.at_bats for stats in historical_stats),
        'total_hits': sum(stats.hits for stats in historical_stats),
        'total_home_runs': sum(stats.home_runs for stats in historical_stats),
        'total_rbi': sum(stats.rbi for stats in historical_stats),
        'total_innings_pitched': sum(stats.innings_pitched for stats in historical_stats),
        'total_wins': sum(stats.wins for stats in historical_stats),
        'total_losses': sum(stats.losses for stats in historical_stats),
        'total_strikeouts': sum(stats.strikeouts for stats in historical_stats)
    }
    
    # 計算平均值
    if career['total_at_bats'] > 0:
        career['career_batting_avg'] = career['total_hits'] / career['total_at_bats']
    else:
        career['career_batting_avg'] = 0
    
    return career
