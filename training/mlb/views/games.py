from flask import Blueprint, render_template, request, jsonify, abort
from models.database import Game, Team, Prediction, db
from datetime import datetime, date, timedelta
from sqlalchemy import or_, and_, func

games_bp = Blueprint('games', __name__)

@games_bp.route('/')
def games_list():
    """比賽列表頁面"""
    page = request.args.get('page', 1, type=int)
    selected_date = request.args.get('date', '')
    selected_status = request.args.get('status', '')
    selected_team = request.args.get('team', '')
    
    # 建立查詢
    query = Game.query
    
    # 日期篩選
    if selected_date:
        try:
            filter_date = datetime.strptime(selected_date, '%Y-%m-%d').date()
            query = query.filter(Game.date == filter_date)
        except ValueError:
            pass
    
    # 狀態篩選
    if selected_status:
        query = query.filter(Game.game_status == selected_status)
    
    # 球隊篩選
    if selected_team:
        query = query.filter(
            or_(Game.home_team == selected_team, Game.away_team == selected_team)
        )
    
    # 分頁
    games = query.order_by(Game.date.desc(), Game.game_id.desc()).paginate(
        page=page, per_page=12, error_out=False
    )
    
    # 獲取所有球隊用於篩選
    teams = Team.query.order_by(Team.team_code).all()
    
    return render_template('games.html',
                         games=games.items,
                         pagination=games,
                         teams=teams,
                         selected_date=selected_date,
                         selected_status=selected_status,
                         selected_team=selected_team)

@games_bp.route('/<game_id>')
def game_detail(game_id):
    """比賽詳情頁面"""
    game = Game.query.filter_by(game_id=game_id).first_or_404()
    
    # 獲取球隊信息
    home_team = Team.query.filter_by(team_code=game.home_team).first()
    away_team = Team.query.filter_by(team_code=game.away_team).first()
    
    # 獲取預測信息
    prediction = Prediction.query.filter_by(game_id=game_id).first()
    
    # 獲取對戰記錄
    h2h_games = Game.query.filter(
        or_(
            and_(Game.home_team == game.home_team, Game.away_team == game.away_team),
            and_(Game.home_team == game.away_team, Game.away_team == game.home_team)
        ),
        Game.game_status == 'completed',
        Game.game_id != game_id
    ).order_by(Game.date.desc()).limit(10).all()
    
    return render_template('game_detail.html',
                         game=game,
                         home_team=home_team,
                         away_team=away_team,
                         prediction=prediction,
                         h2h_games=h2h_games)

@games_bp.route('/<game_id>/analysis')
def game_analysis(game_id):
    """比賽分析頁面"""
    game = Game.query.filter_by(game_id=game_id).first_or_404()
    
    # 獲取球隊信息
    home_team = Team.query.filter_by(team_code=game.home_team).first()
    away_team = Team.query.filter_by(team_code=game.away_team).first()
    
    # 分析數據
    analysis_data = analyze_game_matchup(game, home_team, away_team)
    
    return render_template('game_analysis.html',
                         game=game,
                         home_team=home_team,
                         away_team=away_team,
                         analysis=analysis_data)

@games_bp.route('/today')
def todays_games():
    """今日比賽"""
    today = date.today()
    games = Game.query.filter(Game.date == today).order_by(Game.game_id).all()
    
    return render_template('todays_games.html', games=games, date=today)

@games_bp.route('/api/<game_id>')
def api_game_detail(game_id):
    """API端點：比賽詳情"""
    game = Game.query.filter_by(game_id=game_id).first_or_404()
    
    # 獲取預測
    prediction = Prediction.query.filter_by(game_id=game_id).first()
    
    game_data = game.to_dict()
    if prediction:
        game_data['prediction'] = prediction.to_dict()
    
    return jsonify(game_data)

@games_bp.route('/api/schedule')
def api_schedule():
    """API端點：比賽日程"""
    start_date = request.args.get('start_date', date.today().isoformat())
    end_date = request.args.get('end_date', (date.today() + timedelta(days=7)).isoformat())
    
    try:
        start = datetime.strptime(start_date, '%Y-%m-%d').date()
        end = datetime.strptime(end_date, '%Y-%m-%d').date()
    except ValueError:
        return jsonify({'error': '日期格式錯誤'}), 400
    
    games = Game.query.filter(
        Game.date >= start,
        Game.date <= end
    ).order_by(Game.date, Game.game_id).all()
    
    schedule = {}
    for game in games:
        date_str = game.date.isoformat()
        if date_str not in schedule:
            schedule[date_str] = []
        schedule[date_str].append(game.to_dict())
    
    return jsonify(schedule)

def analyze_game_matchup(game, home_team, away_team):
    """分析比賽對戰"""
    analysis = {
        'home_advantages': [],
        'away_advantages': [],
        'key_factors': [],
        'prediction_factors': {}
    }
    
    # 主場優勢
    if home_team:
        analysis['home_advantages'].append(f"{home_team.team_name} 在 {home_team.venue_name} 主場作戰")
    
    # 獲取最近表現
    home_recent = get_team_recent_form(game.home_team)
    away_recent = get_team_recent_form(game.away_team)
    
    if home_recent['win_rate'] > away_recent['win_rate']:
        analysis['home_advantages'].append(f"最近10場勝率較高 ({home_recent['win_rate']:.1%} vs {away_recent['win_rate']:.1%})")
    else:
        analysis['away_advantages'].append(f"最近10場勝率較高 ({away_recent['win_rate']:.1%} vs {home_recent['win_rate']:.1%})")
    
    # 對戰記錄
    h2h_record = get_head_to_head_record(game.home_team, game.away_team)
    if h2h_record['total_games'] > 0:
        home_wins = h2h_record['home_wins']
        away_wins = h2h_record['total_games'] - home_wins
        
        if home_wins > away_wins:
            analysis['home_advantages'].append(f"對戰記錄佔優 ({home_wins}勝{away_wins}敗)")
        elif away_wins > home_wins:
            analysis['away_advantages'].append(f"對戰記錄佔優 ({away_wins}勝{home_wins}敗)")
    
    # 關鍵因素
    analysis['key_factors'] = [
        "投手對戰",
        "打線深度",
        "牛棚實力",
        "傷兵情況",
        "天氣條件"
    ]
    
    return analysis

def get_team_recent_form(team_code, games_count=10):
    """獲取球隊最近表現"""
    recent_games = Game.query.filter(
        or_(Game.home_team == team_code, Game.away_team == team_code),
        Game.game_status == 'completed'
    ).order_by(Game.date.desc()).limit(games_count).all()
    
    wins = 0
    for game in recent_games:
        if game.home_team == team_code:
            if game.home_score > game.away_score:
                wins += 1
        else:
            if game.away_score > game.home_score:
                wins += 1
    
    return {
        'wins': wins,
        'losses': len(recent_games) - wins,
        'total_games': len(recent_games),
        'win_rate': wins / len(recent_games) if recent_games else 0
    }

def get_head_to_head_record(team1, team2):
    """獲取對戰記錄"""
    h2h_games = Game.query.filter(
        or_(
            and_(Game.home_team == team1, Game.away_team == team2),
            and_(Game.home_team == team2, Game.away_team == team1)
        ),
        Game.game_status == 'completed'
    ).all()
    
    team1_wins = 0
    for game in h2h_games:
        if game.home_team == team1:
            if game.home_score > game.away_score:
                team1_wins += 1
        else:
            if game.away_score > game.home_score:
                team1_wins += 1
    
    return {
        'total_games': len(h2h_games),
        'home_wins': team1_wins,
        'away_wins': len(h2h_games) - team1_wins
    }
