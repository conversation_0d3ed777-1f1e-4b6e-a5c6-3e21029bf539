from flask import Blueprint, render_template, request
from models.database import Game, Team, TeamStats, Prediction, PlayerStats, db
from datetime import datetime, timedelta
import json

dashboard_bp = Blueprint('dashboard', __name__)

@dashboard_bp.route('/')
def dashboard():
    """主儀表板頁面"""
    # 獲取統計數據
    stats = {
        'total_teams': Team.query.count(),
        'total_games': Game.query.count(),
        'total_predictions': Prediction.query.count(),
        'total_players': PlayerStats.query.count()
    }
    
    # 獲取最近的比賽
    recent_games = Game.query.order_by(Game.date.desc()).limit(10).all()
    
    # 獲取最後更新時間
    last_game = Game.query.order_by(Game.updated_at.desc()).first()
    last_update = last_game.updated_at.strftime('%Y-%m-%d %H:%M') if last_game else None
    
    return render_template('dashboard.html',
                         stats=stats,
                         recent_games=recent_games,
                         last_update=last_update)

@dashboard_bp.route('/api/stats')
def api_stats():
    """API端點：獲取統計數據"""
    stats = {
        'teams': Team.query.count(),
        'games': Game.query.count(),
        'predictions': Prediction.query.count(),
        'players': PlayerStats.query.count(),
        'completed_games': Game.query.filter_by(game_status='completed').count(),
        'scheduled_games': Game.query.filter_by(game_status='scheduled').count()
    }
    
    return json.dumps(stats)

@dashboard_bp.route('/recent-games')
def recent_games():
    """近期比賽結果"""
    page = request.args.get('page', 1, type=int)
    
    games = Game.query.filter(
        Game.game_status == 'completed'
    ).order_by(Game.date.desc()).paginate(
        page=page, per_page=20, error_out=False
    )
    
    return render_template('recent_games.html', games=games)

def calculate_recent_accuracy():
    """計算近期預測準確率"""
    # 獲取最近30天的預測結果
    thirty_days_ago = datetime.now() - timedelta(days=30)
    
    completed_predictions = db.session.query(Game, Prediction).join(
        Prediction, Game.game_id == Prediction.game_id
    ).filter(
        Game.game_status == 'completed',
        Game.date >= thirty_days_ago.date()
    ).all()
    
    if not completed_predictions:
        return {'total': 0, 'correct': 0, 'accuracy': 0}
    
    correct_predictions = 0
    total_predictions = len(completed_predictions)
    
    for game, prediction in completed_predictions:
        # 簡單的勝負預測準確性
        predicted_winner = 'home' if prediction.predicted_home_score > prediction.predicted_away_score else 'away'
        actual_winner = 'home' if game.home_score > game.away_score else 'away'
        
        if predicted_winner == actual_winner:
            correct_predictions += 1
    
    accuracy = (correct_predictions / total_predictions) * 100 if total_predictions > 0 else 0
    
    return {
        'total': total_predictions,
        'correct': correct_predictions,
        'accuracy': round(accuracy, 1)
    }
