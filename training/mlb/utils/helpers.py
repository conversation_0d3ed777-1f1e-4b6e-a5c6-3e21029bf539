from datetime import datetime, date, timedelta
from typing import Dict, List, Optional
import pandas as pd
import numpy as np

def calculate_team_form(team_id: int, games: List, last_n_games: int = 10) -> Dict:
    """計算球隊最近N場比賽的狀態"""
    team_games = []
    
    for game in games:
        if game.home_team == team_id or game.away_team == team_id:
            is_home = game.home_team == team_id
            if game.game_status == 'completed':
                if is_home:
                    won = game.home_score > game.away_score
                    runs_for = game.home_score
                    runs_against = game.away_score
                else:
                    won = game.away_score > game.home_score
                    runs_for = game.away_score
                    runs_against = game.home_score
                
                team_games.append({
                    'date': game.date,
                    'won': won,
                    'runs_for': runs_for,
                    'runs_against': runs_against,
                    'is_home': is_home
                })
    
    # 按日期排序，取最近N場
    team_games.sort(key=lambda x: x['date'], reverse=True)
    recent_games = team_games[:last_n_games]
    
    if not recent_games:
        return {
            'wins': 0,
            'losses': 0,
            'win_rate': 0.0,
            'avg_runs_for': 0.0,
            'avg_runs_against': 0.0,
            'form_score': 0.0
        }
    
    wins = sum(1 for game in recent_games if game['won'])
    losses = len(recent_games) - wins
    win_rate = wins / len(recent_games)
    avg_runs_for = sum(game['runs_for'] for game in recent_games) / len(recent_games)
    avg_runs_against = sum(game['runs_against'] for game in recent_games) / len(recent_games)
    
    # 計算狀態分數（勝率 + 得失分差異的標準化值）
    run_diff = avg_runs_for - avg_runs_against
    form_score = win_rate + (run_diff / 10)  # 簡單的狀態評分
    
    return {
        'wins': wins,
        'losses': losses,
        'win_rate': round(win_rate, 3),
        'avg_runs_for': round(avg_runs_for, 2),
        'avg_runs_against': round(avg_runs_against, 2),
        'form_score': round(form_score, 3)
    }

def calculate_head_to_head(team1_id: int, team2_id: int, games: List, season: int = None) -> Dict:
    """計算兩隊對戰記錄"""
    h2h_games = []
    
    for game in games:
        if game.game_status == 'completed':
            if ((game.home_team == team1_id and game.away_team == team2_id) or
                (game.home_team == team2_id and game.away_team == team1_id)):
                
                if season is None or game.date.year == season:
                    h2h_games.append(game)
    
    if not h2h_games:
        return {
            'total_games': 0,
            'team1_wins': 0,
            'team2_wins': 0,
            'team1_win_rate': 0.0,
            'avg_total_runs': 0.0
        }
    
    team1_wins = 0
    total_runs = 0
    
    for game in h2h_games:
        total_runs += (game.home_score or 0) + (game.away_score or 0)
        
        if game.home_team == team1_id:
            if (game.home_score or 0) > (game.away_score or 0):
                team1_wins += 1
        else:  # team1 is away
            if (game.away_score or 0) > (game.home_score or 0):
                team1_wins += 1
    
    team2_wins = len(h2h_games) - team1_wins
    team1_win_rate = team1_wins / len(h2h_games)
    avg_total_runs = total_runs / len(h2h_games)
    
    return {
        'total_games': len(h2h_games),
        'team1_wins': team1_wins,
        'team2_wins': team2_wins,
        'team1_win_rate': round(team1_win_rate, 3),
        'avg_total_runs': round(avg_total_runs, 2)
    }

def get_team_strength_rating(team_stats: Dict) -> float:
    """計算球隊實力評分"""
    if not team_stats:
        return 0.5
    
    # 基於多個指標計算實力評分
    win_rate = team_stats.get('win_percentage', 0)
    ops = team_stats.get('ops', 0)
    era = team_stats.get('era', 5.0)
    
    # 標準化各項指標
    win_rate_score = win_rate  # 已經是0-1之間
    ops_score = min(ops / 1.0, 1.0)  # OPS通常在0.6-1.2之間
    era_score = max(0, (6.0 - era) / 3.0)  # ERA越低越好，6.0為基準
    
    # 加權平均
    strength = (win_rate_score * 0.4 + ops_score * 0.3 + era_score * 0.3)
    
    return round(min(max(strength, 0.1), 0.9), 3)

def format_game_summary(game) -> str:
    """格式化比賽摘要"""
    if game.game_status == 'completed':
        return f"{game.away_team} {game.away_score} - {game.home_score} {game.home_team} (Final)"
    elif game.game_status == 'in_progress':
        inning_info = f"T{game.inning}" if game.inning_state == 'top' else f"B{game.inning}"
        return f"{game.away_team} {game.away_score or 0} - {game.home_score or 0} {game.home_team} ({inning_info})"
    else:
        return f"{game.away_team} @ {game.home_team} ({game.game_status})"

def calculate_prediction_accuracy(predictions: List, actual_games: List) -> Dict:
    """計算預測準確率"""
    if not predictions or not actual_games:
        return {
            'total_predictions': 0,
            'correct_predictions': 0,
            'accuracy': 0.0,
            'avg_score_diff': 0.0
        }
    
    # 建立比賽ID到實際結果的映射
    actual_results = {game.game_id: game for game in actual_games if game.game_status == 'completed'}
    
    correct_predictions = 0
    score_differences = []
    valid_predictions = 0
    
    for pred in predictions:
        if pred.game_id in actual_results:
            actual_game = actual_results[pred.game_id]
            valid_predictions += 1
            
            # 檢查勝負預測是否正確
            predicted_winner = 'home' if pred.predicted_home_score > pred.predicted_away_score else 'away'
            actual_winner = 'home' if actual_game.home_score > actual_game.away_score else 'away'
            
            if predicted_winner == actual_winner:
                correct_predictions += 1
            
            # 計算得分差異
            home_diff = abs(pred.predicted_home_score - actual_game.home_score)
            away_diff = abs(pred.predicted_away_score - actual_game.away_score)
            avg_diff = (home_diff + away_diff) / 2
            score_differences.append(avg_diff)
    
    accuracy = correct_predictions / valid_predictions if valid_predictions > 0 else 0
    avg_score_diff = sum(score_differences) / len(score_differences) if score_differences else 0
    
    return {
        'total_predictions': valid_predictions,
        'correct_predictions': correct_predictions,
        'accuracy': round(accuracy, 3),
        'avg_score_diff': round(avg_score_diff, 2)
    }

def get_date_range(start_date: date, end_date: date) -> List[date]:
    """獲取日期範圍內的所有日期"""
    dates = []
    current_date = start_date
    
    while current_date <= end_date:
        dates.append(current_date)
        current_date += timedelta(days=1)
    
    return dates

def is_mlb_season(check_date: date = None) -> bool:
    """檢查是否在MLB賽季期間"""
    if check_date is None:
        check_date = date.today()
    
    year = check_date.year
    
    # MLB賽季通常從3月底/4月初到10月底
    season_start = date(year, 3, 20)  # 大概的開始時間
    season_end = date(year, 11, 15)   # 包含世界大賽的結束時間
    
    return season_start <= check_date <= season_end

def team_code_to_name_mapping() -> Dict[str, str]:
    """球隊代碼到名稱的映射"""
    return {
        'LAA': 'Los Angeles Angels',
        'HOU': 'Houston Astros',
        'OAK': 'Oakland Athletics',
        'TOR': 'Toronto Blue Jays',
        'ATL': 'Atlanta Braves',
        'MIL': 'Milwaukee Brewers',
        'STL': 'St. Louis Cardinals',
        'CHC': 'Chicago Cubs',
        'ARI': 'Arizona Diamondbacks',
        'LAD': 'Los Angeles Dodgers',
        'SF': 'San Francisco Giants',
        'CLE': 'Cleveland Guardians',
        'SEA': 'Seattle Mariners',
        'MIA': 'Miami Marlins',
        'NYM': 'New York Mets',
        'WSH': 'Washington Nationals',
        'BAL': 'Baltimore Orioles',
        'SD': 'San Diego Padres',
        'PHI': 'Philadelphia Phillies',
        'PIT': 'Pittsburgh Pirates',
        'TEX': 'Texas Rangers',
        'TB': 'Tampa Bay Rays',
        'BOS': 'Boston Red Sox',
        'CIN': 'Cincinnati Reds',
        'COL': 'Colorado Rockies',
        'KC': 'Kansas City Royals',
        'DET': 'Detroit Tigers',
        'MIN': 'Minnesota Twins',
        'CWS': 'Chicago White Sox',
        'NYY': 'New York Yankees'
    }
