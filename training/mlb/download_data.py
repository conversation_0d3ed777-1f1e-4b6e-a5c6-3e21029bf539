#!/usr/bin/env python3
"""
MLB數據下載控制腳本
"""

import sys
import os
import argparse
from datetime import datetime, date, timedelta

# 添加當前目錄到Python路徑
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from models.download_manager import DownloadManager
from models.database import db

def download_historical_data():
    """下載前五年歷史數據"""
    print("=== MLB 歷史數據下載 ===")
    print("準備下載 2019-2024 年的完整MLB數據...")
    
    app = create_app()
    
    with app.app_context():
        manager = DownloadManager()
        
        # 下載前五年數據
        years = [2019, 2020, 2021, 2022, 2023, 2024]
        
        print(f"開始下載 {years} 年的數據...")
        print("這可能需要較長時間，請耐心等待...")
        
        success = manager.download_historical_data(years)
        
        if success:
            print("\n✅ 歷史數據下載完成！")
            
            # 顯示統計信息
            stats = manager.get_download_statistics()
            print("\n📊 下載統計:")
            print(f"  - 球隊數量: {stats['total_teams']}")
            print(f"  - 比賽記錄: {stats['total_games']}")
            print(f"  - 球隊統計: {stats['total_team_stats']}")
            print(f"  - 球員統計: {stats['total_player_stats']}")
            
            if stats['date_range']:
                print(f"  - 數據範圍: {stats['date_range']['start']} 到 {stats['date_range']['end']}")
            
            if stats['games_by_year']:
                print("  - 各年比賽數量:")
                for year, count in sorted(stats['games_by_year'].items()):
                    print(f"    {year}: {count} 場")
        else:
            print("\n❌ 歷史數據下載失敗！")
            progress = manager.load_progress()
            if progress and progress.error_message:
                print(f"錯誤信息: {progress.error_message}")

def download_daily_update():
    """每日數據更新"""
    print("=== MLB 每日數據更新 ===")
    
    app = create_app()
    
    with app.app_context():
        manager = DownloadManager()
        
        today = date.today()
        print(f"更新 {today} 的數據...")
        
        success = manager.download_daily_update(today)
        
        if success:
            print("✅ 每日更新完成！")
        else:
            print("❌ 每日更新失敗！")
            progress = manager.load_progress()
            if progress and progress.error_message:
                print(f"錯誤信息: {progress.error_message}")

def download_date_range(start_date_str: str, end_date_str: str):
    """下載指定日期範圍的數據"""
    print(f"=== 下載 {start_date_str} 到 {end_date_str} 的數據 ===")
    
    try:
        start_date = datetime.strptime(start_date_str, '%Y-%m-%d').date()
        end_date = datetime.strptime(end_date_str, '%Y-%m-%d').date()
    except ValueError:
        print("❌ 日期格式錯誤！請使用 YYYY-MM-DD 格式")
        return
    
    if start_date > end_date:
        print("❌ 開始日期不能晚於結束日期！")
        return
    
    app = create_app()
    
    with app.app_context():
        manager = DownloadManager()
        
        success = manager.download_date_range(start_date, end_date)
        
        if success:
            print("✅ 指定範圍數據下載完成！")
        else:
            print("❌ 指定範圍數據下載失敗！")

def show_download_status():
    """顯示下載狀態"""
    print("=== MLB 數據下載狀態 ===")
    
    app = create_app()
    
    with app.app_context():
        manager = DownloadManager()
        
        # 顯示當前進度
        progress = manager.load_progress()
        if progress:
            print(f"\n📋 最近任務: {progress.task_name}")
            print(f"   狀態: {progress.status}")
            print(f"   開始時間: {progress.start_time}")
            if progress.end_time:
                print(f"   結束時間: {progress.end_time}")
            print(f"   進度: {progress.completed_items}/{progress.total_items} ({progress.progress_percentage:.1f}%)")
            if progress.current_item:
                print(f"   當前項目: {progress.current_item}")
            if progress.error_message:
                print(f"   錯誤信息: {progress.error_message}")
        else:
            print("沒有找到下載進度記錄")
        
        # 顯示數據庫統計
        stats = manager.get_download_statistics()
        print("\n📊 數據庫統計:")
        print(f"  - 球隊數量: {stats['total_teams']}")
        print(f"  - 比賽記錄: {stats['total_games']}")
        print(f"  - 球隊統計: {stats['total_team_stats']}")
        print(f"  - 球員統計: {stats['total_player_stats']}")
        
        if stats['date_range']:
            print(f"  - 數據範圍: {stats['date_range']['start']} 到 {stats['date_range']['end']}")
        
        if stats['last_update']:
            print(f"  - 最後更新: {stats['last_update']}")
        
        if stats['games_by_year']:
            print("  - 各年比賽數量:")
            for year, count in sorted(stats['games_by_year'].items()):
                print(f"    {year}: {count} 場")

def main():
    """主函數"""
    parser = argparse.ArgumentParser(description='MLB數據下載工具')
    parser.add_argument('command', choices=['historical', 'daily', 'range', 'status'], 
                       help='下載命令')
    parser.add_argument('--start-date', help='開始日期 (YYYY-MM-DD)')
    parser.add_argument('--end-date', help='結束日期 (YYYY-MM-DD)')
    
    args = parser.parse_args()
    
    if args.command == 'historical':
        download_historical_data()
    elif args.command == 'daily':
        download_daily_update()
    elif args.command == 'range':
        if not args.start_date or not args.end_date:
            print("❌ 範圍下載需要指定 --start-date 和 --end-date")
            return
        download_date_range(args.start_date, args.end_date)
    elif args.command == 'status':
        show_download_status()

if __name__ == '__main__':
    if len(sys.argv) == 1:
        # 如果沒有參數，顯示幫助信息
        print("MLB數據下載工具")
        print("\n可用命令:")
        print("  python download_data.py historical     # 下載前五年歷史數據")
        print("  python download_data.py daily          # 每日數據更新")
        print("  python download_data.py range --start-date 2024-01-01 --end-date 2024-01-31")
        print("  python download_data.py status         # 顯示下載狀態")
        print("\n範例:")
        print("  python download_data.py historical")
        print("  python download_data.py daily")
        print("  python download_data.py status")
    else:
        main()
