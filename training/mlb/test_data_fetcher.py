#!/usr/bin/env python3
"""
測試數據獲取功能
"""

import sys
import os
from datetime import date, datetime

# 添加當前目錄到Python路徑
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from models.data_fetcher import MLBDataFetcher
from models.database import db, Team, Game

def test_data_fetcher():
    """測試數據獲取器"""
    print("=== MLB 數據獲取測試 ===")
    
    # 創建Flask應用上下文
    app = create_app()
    
    with app.app_context():
        # 創建數據獲取器
        fetcher = MLBDataFetcher()
        
        print("\n1. 測試獲取球隊信息...")
        try:
            teams_data = fetcher.get_teams()
            print(f"成功獲取 {len(teams_data)} 支球隊信息")
            
            if teams_data:
                print("前3支球隊:")
                for i, team in enumerate(teams_data[:3]):
                    print(f"  {i+1}. {team['team_code']}: {team['team_name']}")
        except Exception as e:
            print(f"獲取球隊信息失敗: {e}")
        
        print("\n2. 測試更新球隊到數據庫...")
        try:
            fetcher.update_teams_in_db()
            team_count = Team.query.count()
            print(f"數據庫中現有 {team_count} 支球隊")
            
            if team_count > 0:
                sample_teams = Team.query.limit(3).all()
                print("數據庫中的球隊樣本:")
                for team in sample_teams:
                    print(f"  - {team.team_code}: {team.team_name}")
        except Exception as e:
            print(f"更新球隊信息失敗: {e}")
        
        print("\n3. 測試獲取比賽數據...")
        # 測試多個日期，因為當前可能不是賽季期間
        test_dates = [
            date.today(),
            date(2024, 7, 4),  # 2024年7月4日（賽季中）
            date(2024, 10, 1)  # 2024年10月1日（季後賽）
        ]

        for test_date in test_dates:
            try:
                games_data = fetcher.get_games_for_date(test_date)
                print(f"{test_date} 有 {len(games_data)} 場比賽")

                if games_data:
                    print(f"  {test_date} 比賽:")
                    for game in games_data[:3]:  # 顯示前3場
                        print(f"    - {game['away_team']} @ {game['home_team']} ({game['game_status']})")
                    break  # 找到有比賽的日期就停止
            except Exception as e:
                print(f"獲取 {test_date} 比賽失敗: {e}")

        print("\n4. 測試更新比賽到數據庫...")
        try:
            # 使用有比賽的日期
            test_date = date(2024, 7, 4)
            fetcher.update_games_for_date(test_date)
            game_count = Game.query.filter(Game.date == test_date).count()
            print(f"數據庫中 {test_date} 有 {game_count} 場比賽")
        except Exception as e:
            print(f"更新比賽信息失敗: {e}")
        
        print("\n5. 數據庫統計...")
        try:
            total_teams = Team.query.count()
            total_games = Game.query.count()
            print(f"總計: {total_teams} 支球隊, {total_games} 場比賽")
        except Exception as e:
            print(f"獲取統計信息失敗: {e}")

if __name__ == '__main__':
    test_data_fetcher()
