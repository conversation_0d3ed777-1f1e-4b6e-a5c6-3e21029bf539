#!/usr/bin/env python3
"""
測試擴展的數據獲取功能
"""

import sys
import os
from datetime import date, datetime

# 添加當前目錄到Python路徑
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from models.data_fetcher import MLBDataFetcher
from models.database import db, Team, Game, TeamStats, PlayerStats

def test_extended_data():
    """測試擴展的數據獲取功能"""
    print("=== MLB 擴展數據獲取測試 ===")
    
    # 創建Flask應用上下文
    app = create_app()
    
    with app.app_context():
        # 創建數據獲取器
        fetcher = MLBDataFetcher()
        
        print("\n1. 測試獲取球隊統計數據...")
        try:
            # 選擇一支球隊進行測試
            test_team = Team.query.first()
            if test_team:
                print(f"測試球隊: {test_team.team_code} - {test_team.team_name}")
                
                team_stats = fetcher.get_team_stats(test_team.team_id, 2024)
                if team_stats:
                    print("球隊統計數據:")
                    print(f"  - 勝場: {team_stats.get('wins', 0)}")
                    print(f"  - 敗場: {team_stats.get('losses', 0)}")
                    print(f"  - 勝率: {team_stats.get('win_percentage', 0):.3f}")
                    print(f"  - 打擊率: {team_stats.get('batting_avg', 0):.3f}")
                    print(f"  - 防禦率: {team_stats.get('era', 0):.2f}")
                else:
                    print("  無法獲取球隊統計數據")
            else:
                print("  數據庫中沒有球隊數據")
        except Exception as e:
            print(f"獲取球隊統計失敗: {e}")
        
        print("\n2. 測試獲取進階球隊統計...")
        try:
            if test_team:
                advanced_stats = fetcher.get_advanced_team_stats(test_team.team_id, 2024)
                if advanced_stats:
                    print("進階統計數據:")
                    
                    if 'hitting' in advanced_stats:
                        hitting = advanced_stats['hitting']
                        print(f"  打擊數據:")
                        print(f"    - 得分: {hitting.get('runs', 0)}")
                        print(f"    - 全壘打: {hitting.get('home_runs', 0)}")
                        print(f"    - 打點: {hitting.get('rbi', 0)}")
                        print(f"    - OPS: {hitting.get('ops', 0):.3f}")
                    
                    if 'pitching' in advanced_stats:
                        pitching = advanced_stats['pitching']
                        print(f"  投手數據:")
                        print(f"    - 防禦率: {pitching.get('era', 0):.2f}")
                        print(f"    - WHIP: {pitching.get('whip', 0):.3f}")
                        print(f"    - 三振: {pitching.get('strikeouts', 0)}")
                        print(f"    - 救援成功: {pitching.get('saves', 0)}")
                    
                    if 'fielding' in advanced_stats:
                        fielding = advanced_stats['fielding']
                        print(f"  守備數據:")
                        print(f"    - 守備率: {fielding.get('fielding_pct', 0):.3f}")
                        print(f"    - 失誤: {fielding.get('errors', 0)}")
                        print(f"    - 雙殺: {fielding.get('double_plays', 0)}")
                else:
                    print("  無法獲取進階統計數據")
        except Exception as e:
            print(f"獲取進階統計失敗: {e}")
        
        print("\n3. 測試獲取球員統計數據...")
        try:
            if test_team:
                print(f"獲取 {test_team.team_code} 的球員數據...")
                players_data = fetcher.get_player_stats(test_team.team_id, 2024)
                
                if players_data:
                    print(f"成功獲取 {len(players_data)} 名球員數據")
                    
                    # 顯示前5名球員
                    print("球員樣本:")
                    for i, player in enumerate(players_data[:5]):
                        print(f"  {i+1}. {player.get('player_name', 'Unknown')}")
                        print(f"     位置: {player.get('position', 'N/A')}")
                        print(f"     打擊率: {player.get('batting_avg', 0):.3f}")
                        print(f"     防禦率: {player.get('era', 0):.2f}")
                        print()
                else:
                    print("  無法獲取球員數據")
        except Exception as e:
            print(f"獲取球員統計失敗: {e}")
        
        print("\n4. 測試更新球隊統計到數據庫...")
        try:
            # 更新一支球隊的統計
            if test_team:
                print(f"更新 {test_team.team_code} 的統計數據...")
                fetcher.update_team_stats(2024)
                
                # 檢查數據庫中的統計
                updated_stats = TeamStats.query.filter_by(
                    team_id=test_team.team_id,
                    season=2024
                ).first()
                
                if updated_stats:
                    print("數據庫中的統計數據:")
                    print(f"  - 勝場: {updated_stats.wins}")
                    print(f"  - 敗場: {updated_stats.losses}")
                    print(f"  - 勝率: {updated_stats.win_percentage:.3f}")
                    print(f"  - 打擊率: {updated_stats.batting_avg:.3f}")
                    print(f"  - 防禦率: {updated_stats.era:.2f}")
                else:
                    print("  數據庫中沒有找到統計數據")
        except Exception as e:
            print(f"更新球隊統計失敗: {e}")
        
        print("\n5. 數據庫統計摘要...")
        try:
            total_teams = Team.query.count()
            total_games = Game.query.count()
            total_team_stats = TeamStats.query.count()
            total_player_stats = PlayerStats.query.count()
            
            print(f"  - 球隊數量: {total_teams}")
            print(f"  - 比賽記錄: {total_games}")
            print(f"  - 球隊統計: {total_team_stats}")
            print(f"  - 球員統計: {total_player_stats}")
            
            # 顯示有統計數據的球隊
            teams_with_stats = db.session.query(Team, TeamStats).join(
                TeamStats, Team.team_id == TeamStats.team_id
            ).filter(TeamStats.season == 2024).all()
            
            if teams_with_stats:
                print(f"\n  有2024賽季統計的球隊 ({len(teams_with_stats)}支):")
                for team, stats in teams_with_stats[:10]:  # 顯示前10支
                    print(f"    - {team.team_code}: {stats.wins}勝{stats.losses}敗 (勝率:{stats.win_percentage:.3f})")
            
        except Exception as e:
            print(f"獲取統計摘要失敗: {e}")

if __name__ == '__main__':
    test_extended_data()
