#!/usr/bin/env python3
"""
調試MLB API響應格式
"""

import sys
import os
import json
from datetime import date

# 添加當前目錄到Python路徑
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

import statsapi as mlb

def debug_api_response():
    """調試API響應格式"""
    print("=== MLB API 響應格式調試 ===")
    
    # 測試日期
    test_dates = [
        "2024-07-04",  # 2024年7月4日
        "2024-10-01",  # 2024年10月1日
        "2025-06-21"   # 今天
    ]
    
    for date_str in test_dates:
        print(f"\n📅 測試日期: {date_str}")
        try:
            # 獲取原始API響應
            schedule = mlb.schedule(date=date_str)
            print(f"  API返回類型: {type(schedule)}")
            print(f"  API返回長度: {len(schedule) if hasattr(schedule, '__len__') else 'N/A'}")
            
            if schedule:
                print(f"  第一個項目類型: {type(schedule[0])}")
                print(f"  第一個項目內容:")
                
                # 如果是字典，顯示所有鍵
                if isinstance(schedule[0], dict):
                    keys = list(schedule[0].keys())
                    print(f"    可用鍵: {keys}")
                    
                    # 顯示一些重要字段的值
                    important_fields = ['game_id', 'gamePk', 'status', 'teams', 'home_name', 'away_name']
                    for field in important_fields:
                        if field in schedule[0]:
                            value = schedule[0][field]
                            print(f"    {field}: {value} ({type(value)})")
                
                # 顯示完整的第一個項目（限制長度）
                first_item_str = str(schedule[0])
                if len(first_item_str) > 500:
                    first_item_str = first_item_str[:500] + "..."
                print(f"  第一個項目完整內容: {first_item_str}")
            else:
                print("  沒有比賽數據")
                
        except Exception as e:
            print(f"  錯誤: {e}")
    
    print("\n🔍 測試其他API方法...")
    
    # 測試獲取球隊信息
    try:
        print("\n📋 測試球隊API:")
        teams_data = mlb.get('teams', {'sportId': 1})
        if teams_data and 'teams' in teams_data:
            team = teams_data['teams'][0]
            print(f"  球隊數據樣本: {team.get('abbreviation', 'N/A')} - {team.get('name', 'N/A')}")
    except Exception as e:
        print(f"  球隊API錯誤: {e}")
    
    # 測試獲取今日比賽的不同方法
    try:
        print("\n⚾ 測試今日比賽API (不同方法):")
        today_str = date.today().strftime('%Y-%m-%d')
        
        # 方法1: 使用schedule
        schedule1 = mlb.schedule(date=today_str)
        print(f"  方法1 (schedule): {len(schedule1) if schedule1 else 0} 場比賽")
        
        # 方法2: 使用get方法
        schedule2 = mlb.get('schedule', {'date': today_str})
        print(f"  方法2 (get schedule): {type(schedule2)}")
        
    except Exception as e:
        print(f"  今日比賽API錯誤: {e}")

if __name__ == '__main__':
    debug_api_response()
