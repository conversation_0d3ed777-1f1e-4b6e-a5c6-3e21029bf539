import requests
import pandas as pd
import statsapi as mlb
import json
from datetime import datetime, date, timedelta
from typing import List, Dict, Optional
import logging
import time

from .database import db, Game, Team, TeamStats, PlayerStats, GameDetail, Player

# 設置日誌
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class MLBDataFetcher:
    """MLB數據獲取器"""
    
    def __init__(self):
        self.base_url = "https://statsapi.mlb.com/api/v1"
        self.session = requests.Session()
        
    def get_teams(self) -> List[Dict]:
        """獲取所有MLB球隊信息"""
        try:
            teams_data = mlb.get('teams', {'sportId': 1})
            teams = []
            
            for team in teams_data.get('teams', []):
                team_info = {
                    'team_id': team['id'],
                    'team_code': team['abbreviation'],
                    'team_name': team['name'],
                    'team_name_short': team['teamName'],
                    'division': team.get('division', {}).get('name', ''),
                    'league': team.get('league', {}).get('name', ''),
                    'venue_name': team.get('venue', {}).get('name', ''),
                    'active': team.get('active', True)
                }
                teams.append(team_info)
                
            logger.info(f"獲取到 {len(teams)} 支球隊信息")
            return teams
            
        except Exception as e:
            logger.error(f"獲取球隊信息失敗: {e}")
            return []
    
    def update_teams_in_db(self):
        """更新數據庫中的球隊信息"""
        teams_data = self.get_teams()
        
        for team_data in teams_data:
            existing_team = Team.query.filter_by(team_id=team_data['team_id']).first()
            
            if existing_team:
                # 更新現有球隊信息
                for key, value in team_data.items():
                    setattr(existing_team, key, value)
            else:
                # 創建新球隊
                new_team = Team(**team_data)
                db.session.add(new_team)
        
        try:
            db.session.commit()
            logger.info("球隊信息更新完成")
        except Exception as e:
            db.session.rollback()
            logger.error(f"更新球隊信息失敗: {e}")
    
    def get_games_for_date(self, target_date: date) -> List[Dict]:
        """獲取指定日期的比賽"""
        try:
            date_str = target_date.strftime('%Y-%m-%d')
            schedule = mlb.schedule(date=date_str)
            
            games = []
            for game in schedule:
                # 根據實際API響應格式處理數據
                # 從球隊全名提取縮寫
                home_team = self._extract_team_abbreviation(game.get('home_name', ''))
                away_team = self._extract_team_abbreviation(game.get('away_name', ''))

                # 處理得分（可能是字符串或整數）
                home_score = game.get('home_score')
                away_score = game.get('away_score')

                # 轉換得分為整數（如果不是空字符串）
                try:
                    home_score = int(home_score) if home_score and str(home_score).strip() != '' else None
                except (ValueError, TypeError):
                    home_score = None

                try:
                    away_score = int(away_score) if away_score and str(away_score).strip() != '' else None
                except (ValueError, TypeError):
                    away_score = None

                game_info = {
                    'game_id': str(game.get('game_id', '')),
                    'date': target_date,
                    'home_team': home_team,
                    'away_team': away_team,
                    'home_score': home_score,
                    'away_score': away_score,
                    'game_status': self._map_game_status(game.get('status', 'Scheduled')),
                    'inning': game.get('current_inning'),
                    'inning_state': game.get('inning_state'),
                    'venue': game.get('venue_name', ''),
                }

                # 只添加有效的比賽（有球隊信息）
                if game_info['home_team'] and game_info['away_team']:
                    games.append(game_info)
            
            logger.info(f"獲取到 {target_date} 的 {len(games)} 場比賽")
            return games
            
        except Exception as e:
            logger.error(f"獲取 {target_date} 比賽數據失敗: {e}")
            return []
    
    def _map_game_status(self, status: str) -> str:
        """映射比賽狀態"""
        status_mapping = {
            'Scheduled': 'scheduled',
            'Pre-Game': 'scheduled',
            'In Progress': 'in_progress',
            'Final': 'completed',
            'Game Over': 'completed',
            'Postponed': 'postponed',
            'Cancelled': 'cancelled'
        }
        return status_mapping.get(status, 'scheduled')

    def _extract_team_abbreviation(self, team_name: str) -> str:
        """從球隊全名提取縮寫"""
        # 球隊名稱到縮寫的映射
        team_mapping = {
            'Arizona Diamondbacks': 'AZ',
            'Atlanta Braves': 'ATL',
            'Baltimore Orioles': 'BAL',
            'Boston Red Sox': 'BOS',
            'Chicago Cubs': 'CHC',
            'Chicago White Sox': 'CWS',
            'Cincinnati Reds': 'CIN',
            'Cleveland Guardians': 'CLE',
            'Colorado Rockies': 'COL',
            'Detroit Tigers': 'DET',
            'Houston Astros': 'HOU',
            'Kansas City Royals': 'KC',
            'Los Angeles Angels': 'LAA',
            'Los Angeles Dodgers': 'LAD',
            'Miami Marlins': 'MIA',
            'Milwaukee Brewers': 'MIL',
            'Minnesota Twins': 'MIN',
            'New York Mets': 'NYM',
            'New York Yankees': 'NYY',
            'Oakland Athletics': 'OAK',
            'Athletics': 'ATH',  # 新的奧克蘭運動家隊名稱
            'Philadelphia Phillies': 'PHI',
            'Pittsburgh Pirates': 'PIT',
            'San Diego Padres': 'SD',
            'San Francisco Giants': 'SF',
            'Seattle Mariners': 'SEA',
            'St. Louis Cardinals': 'STL',
            'Tampa Bay Rays': 'TB',
            'Texas Rangers': 'TEX',
            'Toronto Blue Jays': 'TOR',
            'Washington Nationals': 'WSH'
        }

        return team_mapping.get(team_name, team_name[:3].upper())
    
    def update_games_for_date(self, target_date: date):
        """更新指定日期的比賽到數據庫"""
        games_data = self.get_games_for_date(target_date)
        
        for game_data in games_data:
            existing_game = Game.query.filter_by(game_id=game_data['game_id']).first()
            
            if existing_game:
                # 更新現有比賽信息
                for key, value in game_data.items():
                    if key != 'game_id':  # 不更新主鍵
                        setattr(existing_game, key, value)
                existing_game.updated_at = datetime.utcnow()
            else:
                # 創建新比賽記錄
                new_game = Game(**game_data)
                db.session.add(new_game)
        
        try:
            db.session.commit()
            logger.info(f"{target_date} 的比賽信息更新完成")
        except Exception as e:
            db.session.rollback()
            logger.error(f"更新 {target_date} 比賽信息失敗: {e}")
    
    def get_team_stats(self, team_id: int, season: int) -> Optional[Dict]:
        """獲取球隊統計數據"""
        try:
            # 使用MLB Stats API獲取球隊統計
            stats_data = mlb.team_stats(team_id, stats=['season'], groups=['hitting', 'pitching'])
            
            if not stats_data:
                return None
            
            # 解析統計數據
            hitting_stats = {}
            pitching_stats = {}
            
            for stat_group in stats_data:
                if stat_group['group']['displayName'] == 'hitting':
                    hitting_stats = stat_group['stats'][0]['stats']
                elif stat_group['group']['displayName'] == 'pitching':
                    pitching_stats = stat_group['stats'][0]['stats']
            
            # 獲取戰績
            team_record = mlb.team_stats(team_id, stats=['season'], groups=['hitting'])
            wins = losses = 0
            if team_record:
                wins = int(team_record[0]['stats'][0]['stats'].get('wins', 0))
                losses = int(team_record[0]['stats'][0]['stats'].get('losses', 0))
            
            team_stats = {
                'team_id': team_id,
                'season': season,
                'games_played': wins + losses,
                'wins': wins,
                'losses': losses,
                'win_percentage': wins / (wins + losses) if (wins + losses) > 0 else 0,
                'runs_scored': float(hitting_stats.get('runs', 0)),
                'batting_avg': float(hitting_stats.get('avg', 0)),
                'on_base_percentage': float(hitting_stats.get('obp', 0)),
                'slugging_percentage': float(hitting_stats.get('slg', 0)),
                'home_runs': int(hitting_stats.get('homeRuns', 0)),
                'stolen_bases': int(hitting_stats.get('stolenBases', 0)),
                'era': float(pitching_stats.get('era', 0)),
                'whip': float(pitching_stats.get('whip', 0)),
                'strikeouts': int(pitching_stats.get('strikeOuts', 0)),
                'walks': int(pitching_stats.get('baseOnBalls', 0)),
                'saves': int(pitching_stats.get('saves', 0))
            }
            
            # 計算OPS
            team_stats['ops'] = team_stats['on_base_percentage'] + team_stats['slugging_percentage']
            
            return team_stats
            
        except Exception as e:
            logger.error(f"獲取球隊 {team_id} 的 {season} 賽季統計失敗: {e}")
            return None
    
    def update_team_stats(self, season: int = None):
        """更新所有球隊的統計數據"""
        if season is None:
            season = datetime.now().year
        
        teams = Team.query.filter_by(active=True).all()
        
        for team in teams:
            logger.info(f"更新球隊 {team.team_code} 的統計數據...")
            
            stats_data = self.get_team_stats(team.team_id, season)
            if not stats_data:
                continue
            
            existing_stats = TeamStats.query.filter_by(
                team_id=team.team_id, 
                season=season
            ).first()
            
            if existing_stats:
                # 更新現有統計
                for key, value in stats_data.items():
                    if key not in ['team_id', 'season']:
                        setattr(existing_stats, key, value)
                existing_stats.updated_at = datetime.utcnow()
            else:
                # 創建新統計記錄
                new_stats = TeamStats(**stats_data)
                db.session.add(new_stats)
            
            # 每處理幾支球隊就提交一次，避免請求過於頻繁
            time.sleep(1)  # 避免API限制
        
        try:
            db.session.commit()
            logger.info(f"{season} 賽季球隊統計更新完成")
        except Exception as e:
            db.session.rollback()
            logger.error(f"更新球隊統計失敗: {e}")
    
    def update_daily_data(self, target_date: date = None):
        """每日數據更新任務"""
        if target_date is None:
            target_date = date.today()
        
        logger.info(f"開始更新 {target_date} 的數據...")
        
        # 更新球隊信息（如果需要）
        self.update_teams_in_db()
        
        # 更新比賽信息
        self.update_games_for_date(target_date)
        
        # 更新球隊統計（每週更新一次）
        if target_date.weekday() == 0:  # 週一更新
            self.update_team_stats()
        
        logger.info(f"{target_date} 數據更新完成")
    
    def get_recent_games(self, days: int = 7) -> List[Game]:
        """獲取最近幾天的比賽"""
        start_date = date.today() - timedelta(days=days)

        games = Game.query.filter(
            Game.date >= start_date
        ).order_by(Game.date.desc()).all()

        return games

    def get_player_stats(self, team_id: int, season: int = None) -> List[Dict]:
        """獲取球員統計數據"""
        if season is None:
            season = datetime.now().year

        try:
            # 使用MLB Stats API獲取球員統計
            roster_data = mlb.get('teams', {'teamId': team_id, 'rosterType': 'active'})

            players = []
            if roster_data and 'teams' in roster_data and roster_data['teams']:
                team_data = roster_data['teams'][0]
                if 'roster' in team_data:
                    for player_entry in team_data['roster']:
                        player = player_entry.get('person', {})
                        position = player_entry.get('position', {})

                        # 獲取球員詳細統計
                        player_stats = self._get_player_detailed_stats(player.get('id'), season)

                        if player_stats:
                            player_info = {
                                'player_id': player.get('id'),
                                'player_name': player.get('fullName', ''),
                                'team_id': team_id,
                                'season': season,
                                'position': position.get('abbreviation', ''),
                                **player_stats
                            }
                            players.append(player_info)

                        # 避免API限制
                        time.sleep(0.5)

            logger.info(f"獲取到球隊 {team_id} 的 {len(players)} 名球員統計")
            return players

        except Exception as e:
            logger.error(f"獲取球隊 {team_id} 球員統計失敗: {e}")
            return []

    def _get_player_detailed_stats(self, player_id: int, season: int) -> Optional[Dict]:
        """獲取球員詳細統計"""
        try:
            # 獲取打擊統計
            hitting_stats = mlb.player_stats(player_id, stats=['season'], groups=['hitting'])
            # 獲取投手統計
            pitching_stats = mlb.player_stats(player_id, stats=['season'], groups=['pitching'])

            stats = {
                'games_played': 0,
                'at_bats': 0,
                'hits': 0,
                'home_runs': 0,
                'rbi': 0,
                'batting_avg': 0.0,
                'innings_pitched': 0.0,
                'wins': 0,
                'losses': 0,
                'era': 0.0,
                'strikeouts': 0
            }

            # 處理打擊統計
            if hitting_stats:
                for stat_group in hitting_stats:
                    if stat_group.get('group', {}).get('displayName') == 'hitting':
                        hitting_data = stat_group.get('stats', [{}])[0].get('stats', {})
                        stats.update({
                            'games_played': int(hitting_data.get('gamesPlayed', 0)),
                            'at_bats': int(hitting_data.get('atBats', 0)),
                            'hits': int(hitting_data.get('hits', 0)),
                            'home_runs': int(hitting_data.get('homeRuns', 0)),
                            'rbi': int(hitting_data.get('rbi', 0)),
                            'batting_avg': float(hitting_data.get('avg', 0))
                        })

            # 處理投手統計
            if pitching_stats:
                for stat_group in pitching_stats:
                    if stat_group.get('group', {}).get('displayName') == 'pitching':
                        pitching_data = stat_group.get('stats', [{}])[0].get('stats', {})
                        stats.update({
                            'innings_pitched': float(pitching_data.get('inningsPitched', 0)),
                            'wins': int(pitching_data.get('wins', 0)),
                            'losses': int(pitching_data.get('losses', 0)),
                            'era': float(pitching_data.get('era', 0)),
                            'strikeouts': int(pitching_data.get('strikeOuts', 0))
                        })

            return stats

        except Exception as e:
            logger.error(f"獲取球員 {player_id} 詳細統計失敗: {e}")
            return None

    def update_player_stats(self, team_id: int = None, season: int = None):
        """更新球員統計數據"""
        if season is None:
            season = datetime.now().year

        teams_to_update = []
        if team_id:
            team = Team.query.filter_by(team_id=team_id).first()
            if team:
                teams_to_update = [team]
        else:
            teams_to_update = Team.query.filter_by(active=True).all()

        for team in teams_to_update:
            logger.info(f"更新球隊 {team.team_code} 的球員統計...")

            players_data = self.get_player_stats(team.team_id, season)

            for player_data in players_data:
                existing_player = PlayerStats.query.filter_by(
                    player_id=player_data['player_id'],
                    team_id=team.team_id,
                    season=season
                ).first()

                if existing_player:
                    # 更新現有球員統計
                    for key, value in player_data.items():
                        if key not in ['player_id', 'team_id', 'season']:
                            setattr(existing_player, key, value)
                    existing_player.updated_at = datetime.utcnow()
                else:
                    # 創建新球員統計記錄
                    new_player = PlayerStats(**player_data)
                    db.session.add(new_player)

            # 每處理一支球隊就提交一次
            try:
                db.session.commit()
                logger.info(f"球隊 {team.team_code} 球員統計更新完成")
            except Exception as e:
                db.session.rollback()
                logger.error(f"更新球隊 {team.team_code} 球員統計失敗: {e}")

            # 避免API限制
            time.sleep(2)

    def get_advanced_team_stats(self, team_id: int, season: int = None) -> Optional[Dict]:
        """獲取進階球隊統計"""
        if season is None:
            season = datetime.now().year

        try:
            # 獲取更詳細的球隊統計
            advanced_stats = mlb.team_stats(team_id, stats=['season'], groups=['hitting', 'pitching', 'fielding'])

            stats = {}

            for stat_group in advanced_stats:
                group_name = stat_group.get('group', {}).get('displayName', '')
                group_stats = stat_group.get('stats', [{}])[0].get('stats', {})

                if group_name == 'hitting':
                    stats['hitting'] = {
                        'runs': int(group_stats.get('runs', 0)),
                        'doubles': int(group_stats.get('doubles', 0)),
                        'triples': int(group_stats.get('triples', 0)),
                        'home_runs': int(group_stats.get('homeRuns', 0)),
                        'rbi': int(group_stats.get('rbi', 0)),
                        'stolen_bases': int(group_stats.get('stolenBases', 0)),
                        'walks': int(group_stats.get('baseOnBalls', 0)),
                        'strikeouts': int(group_stats.get('strikeOuts', 0)),
                        'batting_avg': float(group_stats.get('avg', 0)),
                        'on_base_pct': float(group_stats.get('obp', 0)),
                        'slugging_pct': float(group_stats.get('slg', 0)),
                        'ops': float(group_stats.get('ops', 0))
                    }
                elif group_name == 'pitching':
                    stats['pitching'] = {
                        'era': float(group_stats.get('era', 0)),
                        'whip': float(group_stats.get('whip', 0)),
                        'strikeouts': int(group_stats.get('strikeOuts', 0)),
                        'walks': int(group_stats.get('baseOnBalls', 0)),
                        'hits_allowed': int(group_stats.get('hits', 0)),
                        'home_runs_allowed': int(group_stats.get('homeRuns', 0)),
                        'saves': int(group_stats.get('saves', 0)),
                        'blown_saves': int(group_stats.get('blownSaves', 0))
                    }
                elif group_name == 'fielding':
                    stats['fielding'] = {
                        'fielding_pct': float(group_stats.get('fielding', 0)),
                        'errors': int(group_stats.get('errors', 0)),
                        'double_plays': int(group_stats.get('doublePlays', 0))
                    }

            return stats

        except Exception as e:
            logger.error(f"獲取球隊 {team_id} 進階統計失敗: {e}")
            return None

    def get_game_detail(self, game_id: str) -> Optional[Dict]:
        """獲取比賽詳細內容"""
        try:
            # 獲取比賽詳細信息
            game_data = mlb.get('game', {'gamePk': game_id})

            if not game_data or 'gameData' not in game_data:
                return None

            game_info = game_data['gameData']
            live_data = game_data.get('liveData', {})

            # 基本信息
            game_detail = {
                'game_id': game_id,
                'attendance': game_info.get('game', {}).get('attendance'),
                'game_duration': game_info.get('game', {}).get('gameDurationMinutes'),
                'weather_condition': None,
                'wind_speed': None,
                'wind_direction': None
            }

            # 天氣信息
            weather = game_info.get('weather', {})
            if weather:
                game_detail.update({
                    'weather_condition': weather.get('condition'),
                    'wind_speed': weather.get('wind'),
                    'wind_direction': weather.get('windDirection')
                })

            # 投手信息
            probable_pitchers = game_info.get('probablePitchers', {})
            game_detail.update({
                'home_starting_pitcher': probable_pitchers.get('home', {}).get('fullName'),
                'away_starting_pitcher': probable_pitchers.get('away', {}).get('fullName')
            })

            # 比賽結果信息
            decisions = live_data.get('decisions', {})
            game_detail.update({
                'winning_pitcher': decisions.get('winner', {}).get('fullName'),
                'losing_pitcher': decisions.get('loser', {}).get('fullName'),
                'save_pitcher': decisions.get('save', {}).get('fullName')
            })

            # 統計數據
            box_score = live_data.get('boxscore', {})
            if box_score:
                teams = box_score.get('teams', {})
                home_stats = teams.get('home', {}).get('teamStats', {})
                away_stats = teams.get('away', {}).get('teamStats', {})

                game_detail.update({
                    'home_hits': home_stats.get('batting', {}).get('hits', 0),
                    'away_hits': away_stats.get('batting', {}).get('hits', 0),
                    'home_errors': home_stats.get('fielding', {}).get('errors', 0),
                    'away_errors': away_stats.get('fielding', {}).get('errors', 0),
                    'home_left_on_base': home_stats.get('batting', {}).get('leftOnBase', 0),
                    'away_left_on_base': away_stats.get('batting', {}).get('leftOnBase', 0)
                })

            # 各局得分
            line_score = live_data.get('linescore', {})
            if line_score and 'innings' in line_score:
                inning_scores = {}
                for inning in line_score['innings']:
                    inning_num = str(inning.get('num', 0))
                    inning_scores[inning_num] = {
                        'home': inning.get('home', {}).get('runs', 0),
                        'away': inning.get('away', {}).get('runs', 0)
                    }
                game_detail['inning_scores'] = json.dumps(inning_scores)

            return game_detail

        except Exception as e:
            logger.error(f"獲取比賽 {game_id} 詳細內容失敗: {e}")
            return None

    def update_game_details(self, game_id: str):
        """更新比賽詳細內容到數據庫"""
        detail_data = self.get_game_detail(game_id)

        if not detail_data:
            return False

        try:
            existing_detail = GameDetail.query.filter_by(game_id=game_id).first()

            if existing_detail:
                # 更新現有詳細信息
                for key, value in detail_data.items():
                    if key != 'game_id':
                        setattr(existing_detail, key, value)
                existing_detail.updated_at = datetime.utcnow()
            else:
                # 創建新詳細信息記錄
                new_detail = GameDetail(**detail_data)
                db.session.add(new_detail)

            db.session.commit()
            logger.info(f"比賽 {game_id} 詳細內容更新完成")
            return True

        except Exception as e:
            db.session.rollback()
            logger.error(f"更新比賽 {game_id} 詳細內容失敗: {e}")
            return False

    def get_player_info(self, player_id: int) -> Optional[Dict]:
        """獲取球員基本信息"""
        try:
            player_data = mlb.get('person', {'personId': player_id})

            if not player_data or 'people' not in player_data:
                return None

            player = player_data['people'][0]

            # 處理生日
            birth_date = None
            if player.get('birthDate'):
                try:
                    birth_date = datetime.strptime(player['birthDate'], '%Y-%m-%d').date()
                except ValueError:
                    pass

            # 處理MLB首秀日期
            mlb_debut = None
            if player.get('mlbDebutDate'):
                try:
                    mlb_debut = datetime.strptime(player['mlbDebutDate'], '%Y-%m-%d').date()
                except ValueError:
                    pass

            player_info = {
                'player_id': player.get('id'),
                'full_name': player.get('fullName', ''),
                'first_name': player.get('firstName', ''),
                'last_name': player.get('lastName', ''),
                'birth_date': birth_date,
                'birth_city': player.get('birthCity', ''),
                'birth_country': player.get('birthCountry', ''),
                'height': player.get('height', ''),
                'weight': player.get('weight'),
                'bats': player.get('batSide', {}).get('code'),
                'throws': player.get('pitchHand', {}).get('code'),
                'primary_position': player.get('primaryPosition', {}).get('abbreviation'),
                'mlb_debut': mlb_debut,
                'current_team_id': player.get('currentTeam', {}).get('id'),
                'jersey_number': player.get('primaryNumber'),
                'active': player.get('active', True)
            }

            return player_info

        except Exception as e:
            logger.error(f"獲取球員 {player_id} 信息失敗: {e}")
            return None

    def update_player_info(self, player_id: int):
        """更新球員基本信息到數據庫"""
        player_data = self.get_player_info(player_id)

        if not player_data:
            return False

        try:
            existing_player = Player.query.filter_by(player_id=player_id).first()

            if existing_player:
                # 更新現有球員信息
                for key, value in player_data.items():
                    if key != 'player_id':
                        setattr(existing_player, key, value)
                existing_player.updated_at = datetime.utcnow()
            else:
                # 創建新球員記錄
                new_player = Player(**player_data)
                db.session.add(new_player)

            db.session.commit()
            logger.info(f"球員 {player_data['full_name']} 信息更新完成")
            return True

        except Exception as e:
            db.session.rollback()
            logger.error(f"更新球員 {player_id} 信息失敗: {e}")
            return False
