import requests
import pandas as pd
import statsapi as mlb
from datetime import datetime, date, timedelta
from typing import List, Dict, Optional
import logging
import time

from .database import db, Game, Team, TeamStats, PlayerStats

# 設置日誌
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class MLBDataFetcher:
    """MLB數據獲取器"""
    
    def __init__(self):
        self.base_url = "https://statsapi.mlb.com/api/v1"
        self.session = requests.Session()
        
    def get_teams(self) -> List[Dict]:
        """獲取所有MLB球隊信息"""
        try:
            teams_data = mlb.get('teams', {'sportId': 1})
            teams = []
            
            for team in teams_data.get('teams', []):
                team_info = {
                    'team_id': team['id'],
                    'team_code': team['abbreviation'],
                    'team_name': team['name'],
                    'team_name_short': team['teamName'],
                    'division': team.get('division', {}).get('name', ''),
                    'league': team.get('league', {}).get('name', ''),
                    'venue_name': team.get('venue', {}).get('name', ''),
                    'active': team.get('active', True)
                }
                teams.append(team_info)
                
            logger.info(f"獲取到 {len(teams)} 支球隊信息")
            return teams
            
        except Exception as e:
            logger.error(f"獲取球隊信息失敗: {e}")
            return []
    
    def update_teams_in_db(self):
        """更新數據庫中的球隊信息"""
        teams_data = self.get_teams()
        
        for team_data in teams_data:
            existing_team = Team.query.filter_by(team_id=team_data['team_id']).first()
            
            if existing_team:
                # 更新現有球隊信息
                for key, value in team_data.items():
                    setattr(existing_team, key, value)
            else:
                # 創建新球隊
                new_team = Team(**team_data)
                db.session.add(new_team)
        
        try:
            db.session.commit()
            logger.info("球隊信息更新完成")
        except Exception as e:
            db.session.rollback()
            logger.error(f"更新球隊信息失敗: {e}")
    
    def get_games_for_date(self, target_date: date) -> List[Dict]:
        """獲取指定日期的比賽"""
        try:
            date_str = target_date.strftime('%Y-%m-%d')
            schedule = mlb.schedule(date=date_str)
            
            games = []
            for game in schedule:
                # 處理不同的API響應格式
                home_team = (game.get('home_name_brief') or
                           game.get('teams', {}).get('home', {}).get('team', {}).get('abbreviation') or
                           game.get('home_team', ''))
                away_team = (game.get('away_name_brief') or
                           game.get('teams', {}).get('away', {}).get('team', {}).get('abbreviation') or
                           game.get('away_team', ''))

                game_info = {
                    'game_id': str(game.get('game_id', game.get('gamePk', ''))),
                    'date': target_date,
                    'home_team': home_team,
                    'away_team': away_team,
                    'home_score': game.get('home_score', game.get('teams', {}).get('home', {}).get('score')),
                    'away_score': game.get('away_score', game.get('teams', {}).get('away', {}).get('score')),
                    'game_status': self._map_game_status(game.get('status', game.get('status', {}).get('detailedState', 'Scheduled'))),
                    'inning': game.get('inning', game.get('linescore', {}).get('currentInning')),
                    'inning_state': game.get('inning_state', game.get('linescore', {}).get('inningState')),
                    'venue': game.get('venue_name', game.get('venue', {}).get('name', '')),
                }

                # 只添加有效的比賽（有球隊信息）
                if game_info['home_team'] and game_info['away_team']:
                    games.append(game_info)
            
            logger.info(f"獲取到 {target_date} 的 {len(games)} 場比賽")
            return games
            
        except Exception as e:
            logger.error(f"獲取 {target_date} 比賽數據失敗: {e}")
            return []
    
    def _map_game_status(self, status: str) -> str:
        """映射比賽狀態"""
        status_mapping = {
            'Scheduled': 'scheduled',
            'Pre-Game': 'scheduled',
            'In Progress': 'in_progress',
            'Final': 'completed',
            'Game Over': 'completed',
            'Postponed': 'postponed',
            'Cancelled': 'cancelled'
        }
        return status_mapping.get(status, 'scheduled')
    
    def update_games_for_date(self, target_date: date):
        """更新指定日期的比賽到數據庫"""
        games_data = self.get_games_for_date(target_date)
        
        for game_data in games_data:
            existing_game = Game.query.filter_by(game_id=game_data['game_id']).first()
            
            if existing_game:
                # 更新現有比賽信息
                for key, value in game_data.items():
                    if key != 'game_id':  # 不更新主鍵
                        setattr(existing_game, key, value)
                existing_game.updated_at = datetime.utcnow()
            else:
                # 創建新比賽記錄
                new_game = Game(**game_data)
                db.session.add(new_game)
        
        try:
            db.session.commit()
            logger.info(f"{target_date} 的比賽信息更新完成")
        except Exception as e:
            db.session.rollback()
            logger.error(f"更新 {target_date} 比賽信息失敗: {e}")
    
    def get_team_stats(self, team_id: int, season: int) -> Optional[Dict]:
        """獲取球隊統計數據"""
        try:
            # 使用MLB Stats API獲取球隊統計
            stats_data = mlb.team_stats(team_id, stats=['season'], groups=['hitting', 'pitching'])
            
            if not stats_data:
                return None
            
            # 解析統計數據
            hitting_stats = {}
            pitching_stats = {}
            
            for stat_group in stats_data:
                if stat_group['group']['displayName'] == 'hitting':
                    hitting_stats = stat_group['stats'][0]['stats']
                elif stat_group['group']['displayName'] == 'pitching':
                    pitching_stats = stat_group['stats'][0]['stats']
            
            # 獲取戰績
            team_record = mlb.team_stats(team_id, stats=['season'], groups=['hitting'])
            wins = losses = 0
            if team_record:
                wins = int(team_record[0]['stats'][0]['stats'].get('wins', 0))
                losses = int(team_record[0]['stats'][0]['stats'].get('losses', 0))
            
            team_stats = {
                'team_id': team_id,
                'season': season,
                'games_played': wins + losses,
                'wins': wins,
                'losses': losses,
                'win_percentage': wins / (wins + losses) if (wins + losses) > 0 else 0,
                'runs_scored': float(hitting_stats.get('runs', 0)),
                'batting_avg': float(hitting_stats.get('avg', 0)),
                'on_base_percentage': float(hitting_stats.get('obp', 0)),
                'slugging_percentage': float(hitting_stats.get('slg', 0)),
                'home_runs': int(hitting_stats.get('homeRuns', 0)),
                'stolen_bases': int(hitting_stats.get('stolenBases', 0)),
                'era': float(pitching_stats.get('era', 0)),
                'whip': float(pitching_stats.get('whip', 0)),
                'strikeouts': int(pitching_stats.get('strikeOuts', 0)),
                'walks': int(pitching_stats.get('baseOnBalls', 0)),
                'saves': int(pitching_stats.get('saves', 0))
            }
            
            # 計算OPS
            team_stats['ops'] = team_stats['on_base_percentage'] + team_stats['slugging_percentage']
            
            return team_stats
            
        except Exception as e:
            logger.error(f"獲取球隊 {team_id} 的 {season} 賽季統計失敗: {e}")
            return None
    
    def update_team_stats(self, season: int = None):
        """更新所有球隊的統計數據"""
        if season is None:
            season = datetime.now().year
        
        teams = Team.query.filter_by(active=True).all()
        
        for team in teams:
            logger.info(f"更新球隊 {team.team_code} 的統計數據...")
            
            stats_data = self.get_team_stats(team.team_id, season)
            if not stats_data:
                continue
            
            existing_stats = TeamStats.query.filter_by(
                team_id=team.team_id, 
                season=season
            ).first()
            
            if existing_stats:
                # 更新現有統計
                for key, value in stats_data.items():
                    if key not in ['team_id', 'season']:
                        setattr(existing_stats, key, value)
                existing_stats.updated_at = datetime.utcnow()
            else:
                # 創建新統計記錄
                new_stats = TeamStats(**stats_data)
                db.session.add(new_stats)
            
            # 每處理幾支球隊就提交一次，避免請求過於頻繁
            time.sleep(1)  # 避免API限制
        
        try:
            db.session.commit()
            logger.info(f"{season} 賽季球隊統計更新完成")
        except Exception as e:
            db.session.rollback()
            logger.error(f"更新球隊統計失敗: {e}")
    
    def update_daily_data(self, target_date: date = None):
        """每日數據更新任務"""
        if target_date is None:
            target_date = date.today()
        
        logger.info(f"開始更新 {target_date} 的數據...")
        
        # 更新球隊信息（如果需要）
        self.update_teams_in_db()
        
        # 更新比賽信息
        self.update_games_for_date(target_date)
        
        # 更新球隊統計（每週更新一次）
        if target_date.weekday() == 0:  # 週一更新
            self.update_team_stats()
        
        logger.info(f"{target_date} 數據更新完成")
    
    def get_recent_games(self, days: int = 7) -> List[Game]:
        """獲取最近幾天的比賽"""
        start_date = date.today() - timedelta(days=days)
        
        games = Game.query.filter(
            Game.date >= start_date
        ).order_by(Game.date.desc()).all()
        
        return games
