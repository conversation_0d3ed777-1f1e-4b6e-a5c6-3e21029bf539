from flask_sqlalchemy import SQLAlchemy
from datetime import datetime

db = SQLAlchemy()

class Game(db.Model):
    """比賽數據模型"""
    __tablename__ = 'games'
    
    id = db.Column(db.Integer, primary_key=True)
    game_id = db.Column(db.String(50), unique=True, nullable=False, index=True)
    date = db.Column(db.Date, nullable=False, index=True)
    home_team = db.Column(db.String(10), nullable=False)
    away_team = db.Column(db.String(10), nullable=False)
    home_score = db.Column(db.Integer)
    away_score = db.Column(db.Integer)
    game_status = db.Column(db.String(20), default='scheduled')  # scheduled, in_progress, completed, postponed
    inning = db.Column(db.Integer)
    inning_state = db.Column(db.String(10))  # top, bottom, end
    venue = db.Column(db.String(100))
    weather = db.Column(db.String(100))
    temperature = db.Column(db.Float)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 關聯到預測
    predictions = db.relationship('Prediction', backref='game', lazy=True)
    
    def __repr__(self):
        return f'<Game {self.away_team} @ {self.home_team} on {self.date}>'
    
    def to_dict(self):
        return {
            'game_id': self.game_id,
            'date': self.date.isoformat() if self.date else None,
            'home_team': self.home_team,
            'away_team': self.away_team,
            'home_score': self.home_score,
            'away_score': self.away_score,
            'status': self.game_status,
            'inning': self.inning,
            'inning_state': self.inning_state,
            'venue': self.venue,
            'weather': self.weather,
            'temperature': self.temperature
        }

class Team(db.Model):
    """球隊基本信息模型"""
    __tablename__ = 'teams'
    
    id = db.Column(db.Integer, primary_key=True)
    team_id = db.Column(db.Integer, unique=True, nullable=False)
    team_code = db.Column(db.String(10), unique=True, nullable=False)
    team_name = db.Column(db.String(100), nullable=False)
    team_name_short = db.Column(db.String(50))
    division = db.Column(db.String(50))
    league = db.Column(db.String(20))
    venue_name = db.Column(db.String(100))
    active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # 關聯到球隊統計
    stats = db.relationship('TeamStats', backref='team', lazy=True)
    
    def __repr__(self):
        return f'<Team {self.team_code}: {self.team_name}>'
    
    def to_dict(self):
        return {
            'team_id': self.team_id,
            'team_code': self.team_code,
            'team_name': self.team_name,
            'team_name_short': self.team_name_short,
            'division': self.division,
            'league': self.league,
            'venue_name': self.venue_name,
            'active': self.active
        }

class TeamStats(db.Model):
    """球隊統計數據模型"""
    __tablename__ = 'team_stats'
    
    id = db.Column(db.Integer, primary_key=True)
    team_id = db.Column(db.Integer, db.ForeignKey('teams.team_id'), nullable=False)
    season = db.Column(db.Integer, nullable=False)
    games_played = db.Column(db.Integer, default=0)
    wins = db.Column(db.Integer, default=0)
    losses = db.Column(db.Integer, default=0)
    win_percentage = db.Column(db.Float, default=0.0)
    
    # 打擊統計
    runs_scored = db.Column(db.Float, default=0.0)
    runs_allowed = db.Column(db.Float, default=0.0)
    batting_avg = db.Column(db.Float, default=0.0)
    on_base_percentage = db.Column(db.Float, default=0.0)
    slugging_percentage = db.Column(db.Float, default=0.0)
    ops = db.Column(db.Float, default=0.0)  # On-base Plus Slugging
    home_runs = db.Column(db.Integer, default=0)
    stolen_bases = db.Column(db.Integer, default=0)
    
    # 投手統計
    era = db.Column(db.Float, default=0.0)  # Earned Run Average
    whip = db.Column(db.Float, default=0.0)  # Walks + Hits per Inning Pitched
    strikeouts = db.Column(db.Integer, default=0)
    walks = db.Column(db.Integer, default=0)
    saves = db.Column(db.Integer, default=0)
    
    # 主客場記錄
    home_wins = db.Column(db.Integer, default=0)
    home_losses = db.Column(db.Integer, default=0)
    away_wins = db.Column(db.Integer, default=0)
    away_losses = db.Column(db.Integer, default=0)
    
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 複合索引
    __table_args__ = (db.Index('idx_team_season', 'team_id', 'season'),)
    
    def __repr__(self):
        return f'<TeamStats {self.team_id} {self.season}: {self.wins}-{self.losses}>'
    
    def to_dict(self):
        return {
            'team_id': self.team_id,
            'season': self.season,
            'games_played': self.games_played,
            'wins': self.wins,
            'losses': self.losses,
            'win_percentage': round(self.win_percentage, 3),
            'runs_scored': round(self.runs_scored, 2),
            'runs_allowed': round(self.runs_allowed, 2),
            'batting_avg': round(self.batting_avg, 3),
            'era': round(self.era, 2),
            'home_record': f"{self.home_wins}-{self.home_losses}",
            'away_record': f"{self.away_wins}-{self.away_losses}"
        }

class Prediction(db.Model):
    """比賽預測模型"""
    __tablename__ = 'predictions'

    id = db.Column(db.Integer, primary_key=True)
    game_id = db.Column(db.String(50), db.ForeignKey('games.game_id'), nullable=False)
    predicted_home_score = db.Column(db.Float, nullable=False)
    predicted_away_score = db.Column(db.Float, nullable=False)
    home_win_probability = db.Column(db.Float, default=0.0)  # 主隊勝率
    away_win_probability = db.Column(db.Float, default=0.0)  # 客隊勝率
    confidence = db.Column(db.Float, default=0.0)  # 預測信心度
    prediction_date = db.Column(db.DateTime, default=datetime.utcnow)
    model_version = db.Column(db.String(20), default='v1.0')

    # 預測特徵（用於分析）
    features_used = db.Column(db.Text)  # JSON格式存儲使用的特徵

    # 預測結果驗證
    is_correct = db.Column(db.Boolean)  # 預測是否正確（比賽結束後更新）
    actual_home_score = db.Column(db.Integer)  # 實際主隊得分
    actual_away_score = db.Column(db.Integer)  # 實際客隊得分
    score_difference = db.Column(db.Float)  # 預測與實際得分差異

    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    def __repr__(self):
        return f'<Prediction {self.game_id}: {self.predicted_away_score:.1f} - {self.predicted_home_score:.1f}>'

    def to_dict(self):
        return {
            'game_id': self.game_id,
            'predicted_home_score': round(self.predicted_home_score, 1),
            'predicted_away_score': round(self.predicted_away_score, 1),
            'home_win_probability': round(self.home_win_probability * 100, 1),
            'away_win_probability': round(self.away_win_probability * 100, 1),
            'confidence': round(self.confidence * 100, 1),
            'prediction_date': self.prediction_date.isoformat(),
            'model_version': self.model_version,
            'is_correct': self.is_correct,
            'score_difference': round(self.score_difference, 2) if self.score_difference else None
        }

class PlayerStats(db.Model):
    """球員統計數據模型（簡化版）"""
    __tablename__ = 'player_stats'

    id = db.Column(db.Integer, primary_key=True)
    player_id = db.Column(db.Integer, nullable=False)
    player_name = db.Column(db.String(100), nullable=False)
    team_id = db.Column(db.Integer, db.ForeignKey('teams.team_id'), nullable=False)
    season = db.Column(db.Integer, nullable=False)
    position = db.Column(db.String(10))

    # 基本統計
    games_played = db.Column(db.Integer, default=0)
    games_started = db.Column(db.Integer, default=0)

    # 打擊統計
    at_bats = db.Column(db.Integer, default=0)
    runs = db.Column(db.Integer, default=0)
    hits = db.Column(db.Integer, default=0)
    doubles = db.Column(db.Integer, default=0)
    triples = db.Column(db.Integer, default=0)
    home_runs = db.Column(db.Integer, default=0)
    rbi = db.Column(db.Integer, default=0)  # Runs Batted In
    walks = db.Column(db.Integer, default=0)  # Base on Balls
    strikeouts_batting = db.Column(db.Integer, default=0)  # 打擊三振
    stolen_bases = db.Column(db.Integer, default=0)
    caught_stealing = db.Column(db.Integer, default=0)

    # 打擊率統計
    batting_avg = db.Column(db.Float, default=0.0)  # AVG
    on_base_percentage = db.Column(db.Float, default=0.0)  # OBP
    slugging_percentage = db.Column(db.Float, default=0.0)  # SLG
    ops = db.Column(db.Float, default=0.0)  # On-base Plus Slugging

    # 投手統計
    innings_pitched = db.Column(db.Float, default=0.0)
    wins = db.Column(db.Integer, default=0)
    losses = db.Column(db.Integer, default=0)
    saves = db.Column(db.Integer, default=0)
    blown_saves = db.Column(db.Integer, default=0)
    holds = db.Column(db.Integer, default=0)

    # 投手率統計
    era = db.Column(db.Float, default=0.0)  # Earned Run Average
    whip = db.Column(db.Float, default=0.0)  # Walks + Hits per Inning Pitched
    strikeouts_pitching = db.Column(db.Integer, default=0)  # 投手三振
    walks_allowed = db.Column(db.Integer, default=0)  # 保送
    hits_allowed = db.Column(db.Integer, default=0)  # 被安打
    home_runs_allowed = db.Column(db.Integer, default=0)  # 被全壘打
    earned_runs = db.Column(db.Integer, default=0)  # 自責分

    # 守備統計
    fielding_percentage = db.Column(db.Float, default=0.0)
    errors = db.Column(db.Integer, default=0)
    assists = db.Column(db.Integer, default=0)
    putouts = db.Column(db.Integer, default=0)
    double_plays = db.Column(db.Integer, default=0)

    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # 複合索引
    __table_args__ = (db.Index('idx_player_team_season', 'player_id', 'team_id', 'season'),)

    def __repr__(self):
        return f'<PlayerStats {self.player_name} ({self.season})>'

    def to_dict(self):
        return {
            'player_id': self.player_id,
            'player_name': self.player_name,
            'team_id': self.team_id,
            'season': self.season,
            'position': self.position,
            'games_played': self.games_played,
            'games_started': self.games_started,

            # 打擊統計
            'at_bats': self.at_bats,
            'runs': self.runs,
            'hits': self.hits,
            'doubles': self.doubles,
            'triples': self.triples,
            'home_runs': self.home_runs,
            'rbi': self.rbi,
            'walks': self.walks,
            'strikeouts_batting': self.strikeouts_batting,
            'stolen_bases': self.stolen_bases,
            'batting_avg': round(self.batting_avg, 3),
            'on_base_percentage': round(self.on_base_percentage, 3),
            'slugging_percentage': round(self.slugging_percentage, 3),
            'ops': round(self.ops, 3),

            # 投手統計
            'innings_pitched': round(self.innings_pitched, 1),
            'wins': self.wins,
            'losses': self.losses,
            'saves': self.saves,
            'era': round(self.era, 2),
            'whip': round(self.whip, 3),
            'strikeouts_pitching': self.strikeouts_pitching,
            'walks_allowed': self.walks_allowed,
            'hits_allowed': self.hits_allowed,

            # 守備統計
            'fielding_percentage': round(self.fielding_percentage, 3),
            'errors': self.errors,
            'assists': self.assists,
            'putouts': self.putouts
        }

class GameDetail(db.Model):
    """比賽詳細內容模型"""
    __tablename__ = 'game_details'

    id = db.Column(db.Integer, primary_key=True)
    game_id = db.Column(db.String(50), db.ForeignKey('games.game_id'), nullable=False)

    # 比賽詳細信息
    attendance = db.Column(db.Integer)  # 觀眾人數
    game_duration = db.Column(db.String(20))  # 比賽時長
    weather_condition = db.Column(db.String(100))  # 天氣狀況
    wind_speed = db.Column(db.String(50))  # 風速
    wind_direction = db.Column(db.String(50))  # 風向

    # 先發投手
    home_starting_pitcher = db.Column(db.String(100))
    away_starting_pitcher = db.Column(db.String(100))

    # 勝負投手
    winning_pitcher = db.Column(db.String(100))
    losing_pitcher = db.Column(db.String(100))
    save_pitcher = db.Column(db.String(100))

    # 比賽摘要
    game_summary = db.Column(db.Text)

    # 各局得分 (JSON格式存儲)
    inning_scores = db.Column(db.Text)  # JSON: {"1": {"home": 0, "away": 1}, ...}

    # 統計數據
    home_hits = db.Column(db.Integer, default=0)
    away_hits = db.Column(db.Integer, default=0)
    home_errors = db.Column(db.Integer, default=0)
    away_errors = db.Column(db.Integer, default=0)
    home_left_on_base = db.Column(db.Integer, default=0)
    away_left_on_base = db.Column(db.Integer, default=0)

    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    def __repr__(self):
        return f'<GameDetail {self.game_id}>'

    def to_dict(self):
        return {
            'game_id': self.game_id,
            'attendance': self.attendance,
            'game_duration': self.game_duration,
            'weather_condition': self.weather_condition,
            'wind_speed': self.wind_speed,
            'wind_direction': self.wind_direction,
            'home_starting_pitcher': self.home_starting_pitcher,
            'away_starting_pitcher': self.away_starting_pitcher,
            'winning_pitcher': self.winning_pitcher,
            'losing_pitcher': self.losing_pitcher,
            'save_pitcher': self.save_pitcher,
            'home_hits': self.home_hits,
            'away_hits': self.away_hits,
            'home_errors': self.home_errors,
            'away_errors': self.away_errors,
            'inning_scores': self.inning_scores
        }

class Player(db.Model):
    """球員基本信息模型"""
    __tablename__ = 'players'

    id = db.Column(db.Integer, primary_key=True)
    player_id = db.Column(db.Integer, unique=True, nullable=False)
    full_name = db.Column(db.String(100), nullable=False)
    first_name = db.Column(db.String(50))
    last_name = db.Column(db.String(50))

    # 基本信息
    birth_date = db.Column(db.Date)
    birth_city = db.Column(db.String(100))
    birth_country = db.Column(db.String(100))
    height = db.Column(db.String(20))  # 例如: "6' 2\""
    weight = db.Column(db.Integer)  # 磅

    # 球員特徵
    bats = db.Column(db.String(1))  # L, R, S (左打、右打、開關打)
    throws = db.Column(db.String(1))  # L, R (左投、右投)
    primary_position = db.Column(db.String(10))  # 主要守備位置

    # 職業信息
    mlb_debut = db.Column(db.Date)  # MLB首秀日期
    current_team_id = db.Column(db.Integer, db.ForeignKey('teams.team_id'))
    jersey_number = db.Column(db.Integer)

    # 狀態
    active = db.Column(db.Boolean, default=True)

    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # 關聯
    stats = db.relationship('PlayerStats',
                           primaryjoin='Player.player_id == PlayerStats.player_id',
                           foreign_keys='PlayerStats.player_id',
                           backref='player_info', lazy=True)

    def __repr__(self):
        return f'<Player {self.full_name}>'

    def to_dict(self):
        return {
            'player_id': self.player_id,
            'full_name': self.full_name,
            'first_name': self.first_name,
            'last_name': self.last_name,
            'birth_date': self.birth_date.isoformat() if self.birth_date else None,
            'birth_city': self.birth_city,
            'birth_country': self.birth_country,
            'height': self.height,
            'weight': self.weight,
            'bats': self.bats,
            'throws': self.throws,
            'primary_position': self.primary_position,
            'mlb_debut': self.mlb_debut.isoformat() if self.mlb_debut else None,
            'current_team_id': self.current_team_id,
            'jersey_number': self.jersey_number,
            'active': self.active
        }
