import os
import json
import logging
from datetime import datetime, date, timedelta
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
import time

from .data_fetcher import MLBDataFetcher
from .database import db, Game, Team, TeamStats, PlayerStats

# 設置日誌
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class DownloadProgress:
    """下載進度數據類"""
    task_id: str
    task_name: str
    start_time: datetime
    end_time: Optional[datetime] = None
    total_items: int = 0
    completed_items: int = 0
    current_item: str = ""
    status: str = "running"  # running, completed, failed, cancelled
    error_message: str = ""
    
    @property
    def progress_percentage(self) -> float:
        if self.total_items == 0:
            return 0.0
        return (self.completed_items / self.total_items) * 100
    
    @property
    def is_completed(self) -> bool:
        return self.status in ['completed', 'failed', 'cancelled']

class DownloadManager:
    """MLB數據下載管理器"""
    
    def __init__(self):
        self.fetcher = MLBDataFetcher()
        self.progress_file = "data/download_progress.json"
        self.current_progress: Optional[DownloadProgress] = None
        
        # 確保進度文件目錄存在
        os.makedirs(os.path.dirname(self.progress_file), exist_ok=True)
    
    def save_progress(self):
        """保存下載進度"""
        if self.current_progress:
            progress_data = {
                'task_id': self.current_progress.task_id,
                'task_name': self.current_progress.task_name,
                'start_time': self.current_progress.start_time.isoformat(),
                'end_time': self.current_progress.end_time.isoformat() if self.current_progress.end_time else None,
                'total_items': self.current_progress.total_items,
                'completed_items': self.current_progress.completed_items,
                'current_item': self.current_progress.current_item,
                'status': self.current_progress.status,
                'error_message': self.current_progress.error_message
            }
            
            try:
                with open(self.progress_file, 'w', encoding='utf-8') as f:
                    json.dump(progress_data, f, ensure_ascii=False, indent=2)
            except Exception as e:
                logger.error(f"保存進度失敗: {e}")
    
    def load_progress(self) -> Optional[DownloadProgress]:
        """載入下載進度"""
        try:
            if os.path.exists(self.progress_file):
                with open(self.progress_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                progress = DownloadProgress(
                    task_id=data['task_id'],
                    task_name=data['task_name'],
                    start_time=datetime.fromisoformat(data['start_time']),
                    end_time=datetime.fromisoformat(data['end_time']) if data['end_time'] else None,
                    total_items=data['total_items'],
                    completed_items=data['completed_items'],
                    current_item=data['current_item'],
                    status=data['status'],
                    error_message=data['error_message']
                )
                return progress
        except Exception as e:
            logger.error(f"載入進度失敗: {e}")
        
        return None
    
    def start_download_task(self, task_name: str, total_items: int) -> str:
        """開始下載任務"""
        task_id = f"download_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        self.current_progress = DownloadProgress(
            task_id=task_id,
            task_name=task_name,
            start_time=datetime.now(),
            total_items=total_items,
            status="running"
        )
        
        self.save_progress()
        logger.info(f"開始下載任務: {task_name} (ID: {task_id})")
        return task_id
    
    def update_progress(self, completed_items: int, current_item: str = ""):
        """更新下載進度"""
        if self.current_progress:
            self.current_progress.completed_items = completed_items
            self.current_progress.current_item = current_item
            self.save_progress()
            
            if completed_items % 10 == 0 or completed_items == self.current_progress.total_items:
                logger.info(f"進度: {completed_items}/{self.current_progress.total_items} "
                          f"({self.current_progress.progress_percentage:.1f}%) - {current_item}")
    
    def complete_task(self, success: bool = True, error_message: str = ""):
        """完成下載任務"""
        if self.current_progress:
            self.current_progress.end_time = datetime.now()
            self.current_progress.status = "completed" if success else "failed"
            self.current_progress.error_message = error_message
            self.save_progress()
            
            duration = self.current_progress.end_time - self.current_progress.start_time
            logger.info(f"任務完成: {self.current_progress.task_name} "
                       f"(耗時: {duration}, 狀態: {self.current_progress.status})")
    
    def download_date_range(self, start_date: date, end_date: date, 
                          include_games: bool = True, 
                          include_team_stats: bool = True,
                          include_player_stats: bool = False) -> bool:
        """下載指定日期範圍的數據"""
        
        # 計算總天數
        total_days = (end_date - start_date).days + 1
        task_name = f"下載 {start_date} 到 {end_date} 的數據"
        
        task_id = self.start_download_task(task_name, total_days)
        
        try:
            current_date = start_date
            completed_days = 0
            
            while current_date <= end_date:
                date_str = current_date.strftime('%Y-%m-%d')
                self.update_progress(completed_days, f"處理 {date_str}")
                
                # 下載比賽數據
                if include_games:
                    try:
                        self.fetcher.update_games_for_date(current_date)
                        logger.info(f"✓ {date_str} 比賽數據下載完成")
                    except Exception as e:
                        logger.error(f"✗ {date_str} 比賽數據下載失敗: {e}")
                
                # 每週更新一次球隊統計（週一）
                if include_team_stats and current_date.weekday() == 0:
                    try:
                        year = current_date.year
                        self.fetcher.update_team_stats(year)
                        logger.info(f"✓ {year} 球隊統計更新完成")
                    except Exception as e:
                        logger.error(f"✗ {year} 球隊統計更新失敗: {e}")
                
                # 避免API限制
                time.sleep(1)
                
                current_date += timedelta(days=1)
                completed_days += 1
            
            self.update_progress(completed_days, "下載完成")
            self.complete_task(success=True)
            return True
            
        except Exception as e:
            error_msg = f"下載過程中發生錯誤: {e}"
            logger.error(error_msg)
            self.complete_task(success=False, error_message=error_msg)
            return False
    
    def download_season_data(self, year: int) -> bool:
        """下載整個賽季的數據"""
        # MLB賽季通常從3月底到10月底
        start_date = date(year, 3, 20)
        end_date = date(year, 11, 15)
        
        return self.download_date_range(
            start_date, end_date,
            include_games=True,
            include_team_stats=True,
            include_player_stats=False  # 球員數據較大，暫時不包含
        )
    
    def download_historical_data(self, years: List[int]) -> bool:
        """下載多年歷史數據"""
        task_name = f"下載 {min(years)}-{max(years)} 年歷史數據"
        total_years = len(years)
        
        task_id = self.start_download_task(task_name, total_years)
        
        try:
            for i, year in enumerate(years):
                self.update_progress(i, f"下載 {year} 年數據")
                
                success = self.download_season_data(year)
                if not success:
                    error_msg = f"{year} 年數據下載失敗"
                    self.complete_task(success=False, error_message=error_msg)
                    return False
                
                logger.info(f"✓ {year} 年數據下載完成")
            
            self.update_progress(total_years, "所有歷史數據下載完成")
            self.complete_task(success=True)
            return True
            
        except Exception as e:
            error_msg = f"歷史數據下載失敗: {e}"
            logger.error(error_msg)
            self.complete_task(success=False, error_message=error_msg)
            return False
    
    def download_daily_update(self, target_date: date = None) -> bool:
        """每日數據更新"""
        if target_date is None:
            target_date = date.today()
        
        task_name = f"每日更新 {target_date}"
        task_id = self.start_download_task(task_name, 3)  # 3個步驟
        
        try:
            # 步驟1: 更新球隊信息
            self.update_progress(0, "更新球隊信息")
            self.fetcher.update_teams_in_db()
            
            # 步驟2: 更新比賽數據
            self.update_progress(1, f"更新 {target_date} 比賽數據")
            self.fetcher.update_games_for_date(target_date)
            
            # 步驟3: 更新球隊統計（每週一次）
            if target_date.weekday() == 0:  # 週一
                self.update_progress(2, "更新球隊統計")
                self.fetcher.update_team_stats(target_date.year)
            
            self.update_progress(3, "每日更新完成")
            self.complete_task(success=True)
            return True
            
        except Exception as e:
            error_msg = f"每日更新失敗: {e}"
            logger.error(error_msg)
            self.complete_task(success=False, error_message=error_msg)
            return False
    
    def get_download_statistics(self) -> Dict:
        """獲取下載統計信息"""
        stats = {
            'total_teams': Team.query.count(),
            'total_games': Game.query.count(),
            'total_team_stats': TeamStats.query.count(),
            'total_player_stats': PlayerStats.query.count(),
            'date_range': {},
            'games_by_year': {},
            'last_update': None
        }
        
        # 獲取比賽日期範圍
        first_game = Game.query.order_by(Game.date.asc()).first()
        last_game = Game.query.order_by(Game.date.desc()).first()
        
        if first_game and last_game:
            stats['date_range'] = {
                'start': first_game.date.isoformat(),
                'end': last_game.date.isoformat()
            }
            stats['last_update'] = last_game.updated_at.isoformat() if last_game.updated_at else None
        
        # 按年份統計比賽數量
        from sqlalchemy import func, extract
        games_by_year = db.session.query(
            extract('year', Game.date).label('year'),
            func.count(Game.id).label('count')
        ).group_by(extract('year', Game.date)).all()
        
        stats['games_by_year'] = {int(year): count for year, count in games_by_year}
        
        return stats
