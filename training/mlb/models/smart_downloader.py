import os
import json
import logging
from datetime import datetime, date, timedelta
from typing import Dict, List, Optional, Tuple, Set
from dataclasses import dataclass
import time

from .download_manager import DownloadManager
from .data_fetcher import MLBDataFetcher
from .database import db, Game, Team, TeamStats, PlayerStats, GameDetail, Player

# 設置日誌
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class DownloadPlan:
    """下載計劃數據類"""
    missing_dates: List[date]
    total_days: int
    estimated_time_minutes: int
    priority_dates: List[date]  # 優先下載的日期
    
    def to_dict(self):
        return {
            'missing_dates': [d.isoformat() for d in self.missing_dates],
            'total_days': self.total_days,
            'estimated_time_minutes': self.estimated_time_minutes,
            'priority_dates': [d.isoformat() for d in self.priority_dates]
        }

class SmartDownloader:
    """智能MLB數據下載器"""
    
    def __init__(self):
        self.download_manager = DownloadManager()
        self.fetcher = MLBDataFetcher()
        self.config_file = "data/download_config.json"
        self.load_config()
    
    def load_config(self):
        """載入下載配置"""
        default_config = {
            'start_year': 2019,  # 前五年開始
            'max_daily_downloads': 100,  # 每日最大下載天數
            'priority_recent_days': 30,  # 優先下載最近30天
            'batch_size': 7,  # 批次大小（天）
            'retry_failed_dates': True,
            'include_game_details': True,
            'include_player_info': True
        }
        
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    self.config = {**default_config, **json.load(f)}
            else:
                self.config = default_config
                self.save_config()
        except Exception as e:
            logger.error(f"載入配置失敗: {e}")
            self.config = default_config
    
    def save_config(self):
        """保存下載配置"""
        try:
            os.makedirs(os.path.dirname(self.config_file), exist_ok=True)
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.error(f"保存配置失敗: {e}")
    
    def analyze_missing_data(self) -> DownloadPlan:
        """分析缺失的數據"""
        logger.info("分析數據庫中缺失的數據...")
        
        # 計算需要下載的日期範圍
        start_date = date(self.config['start_year'], 3, 20)  # MLB賽季開始
        end_date = date.today()
        
        # 獲取數據庫中已有的日期
        existing_dates = set()
        games = Game.query.with_entities(Game.date).distinct().all()
        for game_date, in games:
            existing_dates.add(game_date)
        
        # 找出缺失的日期
        missing_dates = []
        current_date = start_date
        
        while current_date <= end_date:
            # 只在MLB賽季期間下載
            if self.is_mlb_season_date(current_date):
                if current_date not in existing_dates:
                    missing_dates.append(current_date)
            current_date += timedelta(days=1)
        
        # 設置優先級（最近的日期優先）
        recent_cutoff = date.today() - timedelta(days=self.config['priority_recent_days'])
        priority_dates = [d for d in missing_dates if d >= recent_cutoff]
        
        # 估算下載時間（每天約1分鐘）
        estimated_time = len(missing_dates) * 1
        
        plan = DownloadPlan(
            missing_dates=missing_dates,
            total_days=len(missing_dates),
            estimated_time_minutes=estimated_time,
            priority_dates=priority_dates
        )
        
        logger.info(f"分析完成: 需要下載 {len(missing_dates)} 天的數據")
        logger.info(f"優先下載: {len(priority_dates)} 天的最近數據")
        logger.info(f"預估時間: {estimated_time} 分鐘")
        
        return plan
    
    def is_mlb_season_date(self, check_date: date) -> bool:
        """檢查是否在MLB賽季期間"""
        year = check_date.year
        
        # MLB賽季通常從3月底到11月中
        season_start = date(year, 3, 20)
        season_end = date(year, 11, 15)
        
        return season_start <= check_date <= season_end
    
    def download_complete_history(self, force_redownload: bool = False) -> bool:
        """下載完整的歷史數據"""
        logger.info("=== 開始下載完整的MLB歷史數據 ===")
        
        if not force_redownload:
            plan = self.analyze_missing_data()
            if plan.total_days == 0:
                logger.info("✅ 所有數據已是最新，無需下載")
                return True
            
            logger.info(f"📋 下載計劃: {plan.total_days} 天數據")
            missing_dates = plan.missing_dates
        else:
            # 強制重新下載所有數據
            start_date = date(self.config['start_year'], 3, 20)
            end_date = date.today()
            missing_dates = []
            
            current_date = start_date
            while current_date <= end_date:
                if self.is_mlb_season_date(current_date):
                    missing_dates.append(current_date)
                current_date += timedelta(days=1)
        
        # 分批下載
        batch_size = self.config['batch_size']
        total_batches = (len(missing_dates) + batch_size - 1) // batch_size
        
        logger.info(f"📦 將分 {total_batches} 個批次下載，每批次 {batch_size} 天")
        
        success_count = 0
        failed_dates = []
        
        for i in range(0, len(missing_dates), batch_size):
            batch_dates = missing_dates[i:i + batch_size]
            batch_num = i // batch_size + 1
            
            logger.info(f"📥 開始第 {batch_num}/{total_batches} 批次下載...")
            
            start_date = batch_dates[0]
            end_date = batch_dates[-1]
            
            success = self.download_manager.download_date_range(
                start_date, end_date,
                include_games=True,
                include_team_stats=(batch_num == 1),  # 只在第一批次更新球隊統計
                include_player_stats=False
            )
            
            if success:
                success_count += len(batch_dates)
                logger.info(f"✅ 第 {batch_num} 批次下載完成")
                
                # 下載比賽詳細內容（對於已完成的比賽）
                if self.config['include_game_details']:
                    self.download_game_details_for_dates(batch_dates)
                
            else:
                failed_dates.extend(batch_dates)
                logger.error(f"❌ 第 {batch_num} 批次下載失敗")
            
            # 批次間休息
            if i + batch_size < len(missing_dates):
                logger.info("⏸️ 批次間休息 5 秒...")
                time.sleep(5)
        
        # 總結
        logger.info(f"📊 下載完成統計:")
        logger.info(f"  - 成功下載: {success_count} 天")
        logger.info(f"  - 失敗: {len(failed_dates)} 天")
        
        if failed_dates:
            logger.warning(f"  - 失敗日期: {[d.isoformat() for d in failed_dates[:10]]}")
            if len(failed_dates) > 10:
                logger.warning(f"    ... 還有 {len(failed_dates) - 10} 個失敗日期")
        
        return len(failed_dates) == 0
    
    def download_game_details_for_dates(self, dates: List[date]):
        """為指定日期下載比賽詳細內容"""
        logger.info(f"📋 下載 {len(dates)} 天的比賽詳細內容...")
        
        for target_date in dates:
            games = Game.query.filter(
                Game.date == target_date,
                Game.game_status == 'completed'
            ).all()
            
            for game in games:
                try:
                    # 檢查是否已有詳細內容
                    existing_detail = GameDetail.query.filter_by(game_id=game.game_id).first()
                    if not existing_detail:
                        self.fetcher.update_game_details(game.game_id)
                        time.sleep(0.5)  # 避免API限制
                except Exception as e:
                    logger.error(f"下載比賽 {game.game_id} 詳細內容失敗: {e}")
    
    def download_daily_update(self) -> bool:
        """每日增量更新"""
        logger.info("=== 執行每日數據更新 ===")
        
        today = date.today()
        yesterday = today - timedelta(days=1)
        
        # 更新最近3天的數據（確保完整性）
        update_dates = [today - timedelta(days=i) for i in range(3)]
        
        success = True
        for update_date in update_dates:
            if self.is_mlb_season_date(update_date):
                logger.info(f"📅 更新 {update_date} 的數據...")
                
                date_success = self.download_manager.download_date_range(
                    update_date, update_date,
                    include_games=True,
                    include_team_stats=(update_date.weekday() == 0),  # 週一更新統計
                    include_player_stats=False
                )
                
                if not date_success:
                    success = False
                    logger.error(f"❌ {update_date} 數據更新失敗")
                else:
                    logger.info(f"✅ {update_date} 數據更新完成")
        
        return success
    
    def get_download_status(self) -> Dict:
        """獲取下載狀態"""
        stats = self.download_manager.get_download_statistics()
        plan = self.analyze_missing_data()
        
        # 計算完整性百分比
        total_possible_days = 0
        start_date = date(self.config['start_year'], 3, 20)
        end_date = date.today()
        
        current_date = start_date
        while current_date <= end_date:
            if self.is_mlb_season_date(current_date):
                total_possible_days += 1
            current_date += timedelta(days=1)
        
        completeness = ((total_possible_days - plan.total_days) / total_possible_days * 100) if total_possible_days > 0 else 0
        
        status = {
            'database_stats': stats,
            'download_plan': plan.to_dict(),
            'completeness_percentage': round(completeness, 1),
            'total_possible_days': total_possible_days,
            'config': self.config,
            'last_analysis': datetime.now().isoformat()
        }
        
        return status
    
    def repair_missing_data(self) -> bool:
        """修復缺失的數據"""
        logger.info("=== 開始修復缺失的數據 ===")
        
        plan = self.analyze_missing_data()
        
        if plan.total_days == 0:
            logger.info("✅ 沒有發現缺失的數據")
            return True
        
        # 優先修復最近的數據
        if plan.priority_dates:
            logger.info(f"🔧 優先修復最近 {len(plan.priority_dates)} 天的數據...")
            
            for priority_date in plan.priority_dates[:self.config['max_daily_downloads']]:
                success = self.download_manager.download_date_range(
                    priority_date, priority_date,
                    include_games=True,
                    include_team_stats=False,
                    include_player_stats=False
                )
                
                if success:
                    logger.info(f"✅ 修復 {priority_date} 完成")
                else:
                    logger.error(f"❌ 修復 {priority_date} 失敗")
                
                time.sleep(1)  # 避免API限制
        
        return True
